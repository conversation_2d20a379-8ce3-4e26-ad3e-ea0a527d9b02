import Foundation
import Cocoa

/// Уровни пользователя для адаптации системы очков
enum UserLevel: String, CaseIterable {
    case beginner = "beginner"     // 3+ дня без работы
    case starter = "starter"       // 1-2 дня работы
    case regular = "regular"       // 3+ дня стабильной работы
    case advanced = "advanced"     // Высокий уровень продуктивности
    
    var displayName: String {
        switch self {
        case .beginner: return "Новичок"
        case .starter: return "Начинающий"
        case .regular: return "Обычный"
        case .advanced: return "Продвинутый"
        }
    }
}

/// Источники отказов для отслеживания
enum RefusalSource {
    case microSession      // MicroSessionOfferWindow "Нет"
    case breakEndLong      // BreakEndWindow после 17 мин "Later"  
    case breakEndMedium    // BreakEndWindow после 15 мин "Later"
    case gentleAfternoon   // GentleAfternoonOfferWindow "Нет"
}

/// Менеджер ежедневной нагрузки - центральная система управления предложениями работы
class DailyWorkloadManager {
    static let shared = DailyWorkloadManager()

    // MARK: - Режим тестирования
    /// РЕЖИМ БЫСТРОГО ТЕСТИРОВАНИЯ (30 сек вместо 30 мин)
    /// Читается из настроек пользователя
    private var isTestMode: Bool {
        return UserDefaults.standard.bool(forKey: "DailyWorkloadFastTestMode")
    }
    var testModeMultiplier: TimeInterval {
        return isTestMode ? (1.0 / 60.0) : 1.0 // 1/60 = секунды вместо минут
    }

    // MARK: - Properties
    
    /// Текущая планка очков (аналог currentUserBar)
    private var currentPointsBar: Int {
        get { 
            let value = UserDefaults.standard.integer(forKey: "dailyPointsBar")
            return value == 0 ? getInitialPointsBar(level: userLevel) : value
        }
        set { UserDefaults.standard.set(newValue, forKey: "dailyPointsBar") }
    }
    
    /// Количество отказов от микросессий сегодня
    private var microSessionRefusals: Int {
        get { UserDefaults.standard.integer(forKey: "microSessionRefusals_\(todayDateString())") }
        set { UserDefaults.standard.set(newValue, forKey: "microSessionRefusals_\(todayDateString())") }
    }
    
    /// Количество отказов от BreakEndWindow сегодня  
    private var breakEndRefusals: Int {
        get { UserDefaults.standard.integer(forKey: "breakEndRefusals_\(todayDateString())") }
        set { UserDefaults.standard.set(newValue, forKey: "breakEndRefusals_\(todayDateString())") }
    }
    
    /// Время последнего отказа
    private var lastRefusalTime: Date? {
        get { UserDefaults.standard.object(forKey: "lastRefusalTime") as? Date }
        set { UserDefaults.standard.set(newValue, forKey: "lastRefusalTime") }
    }
    
    /// Текущий уровень пользователя
    var userLevel: UserLevel = .beginner
    
    /// Рабочие часы (из настроек)
    private var workingHours: (start: Int, end: Int) {
        let start = UserDefaults.standard.integer(forKey: "workingHoursStart")
        let end = UserDefaults.standard.integer(forKey: "workingHoursEnd")
        return (start: start == 0 ? 9 : start, end: end == 0 ? 18 : end)
    }
    
    // MARK: - Initialization
    
    private init() {
        // Определяем текущий уровень пользователя
        updateUserLevel()
        
        // Сбрасываем счетчики в начале нового дня
        resetDailyCountersIfNeeded()
        
        Logger.shared.log(.info, "DailyWorkload", "🎯 Инициализирован. Уровень: \(userLevel.displayName), Планка: \(currentPointsBar) очков")
    }
    
    // MARK: - Public Methods
    
    /// Проверяет можно ли предложить сессию
    func shouldOfferSession() -> Bool {
        // 1. Проверяем рабочие часы
        guard isWithinWorkingHours() else { 
            Logger.shared.log(.debug, "DailyWorkload", "❌ Вне рабочих часов")
            return false 
        }
        
        // 2. Проверяем достижение дневной цели
        let progress = getDailyProgress()
        if progress >= 1.0 {
            let shouldOffer = shouldOfferBonusSession()
            Logger.shared.log(.debug, "DailyWorkload", "🎯 Цель достигнута (\(Int(progress*100))%). Бонусное предложение: \(shouldOffer)")
            return shouldOffer
        }
        
        // 3. Проверяем количество отказов от микросессий
        if microSessionRefusals >= 3 {
            Logger.shared.log(.debug, "DailyWorkload", "❌ Слишком много отказов от микросессий (\(microSessionRefusals))")
            return false
        }
        
        Logger.shared.log(.debug, "DailyWorkload", "✅ Можно предложить сессию. Прогресс: \(Int(progress*100))%")
        return true
    }
    
    /// Возвращает адаптированный интервал между предложениями
    func getNextOfferInterval(_ baseInterval: TimeInterval) -> TimeInterval {
        var interval = baseInterval
        
        // Адаптация по времени дня
        interval *= getTimeOfDayMultiplier()
        
        // Адаптация по отказам (только для микросессий)
        interval *= getRefusalMultiplier(refusals: microSessionRefusals)
        
        // Адаптация по прогрессу к цели
        interval *= getProgressMultiplier()
        
        Logger.shared.log(.debug, "DailyWorkload", "⏰ Интервал: \(Int(baseInterval/60))мин → \(Int(interval/60))мин")
        return interval
    }
    
    /// Рассчитывает очки за сессию
    func calculateSessionPoints(minutes: Int) -> Int {
        let basePoints = minutes
        let contextBonus = calculateContextBonus(minutes: minutes, level: userLevel)
        let totalPoints = basePoints + contextBonus
        
        Logger.shared.log(.debug, "DailyWorkload", "📊 Сессия \(minutes)мин = \(basePoints) + \(contextBonus) = \(totalPoints) очков")
        return totalPoints
    }
    
    /// Обрабатывает завершение сессии
    func handleSessionCompleted(minutes: Int) {
        let points = calculateSessionPoints(minutes: minutes)
        addTodayPoints(points)
        
        // Сбрасываем счетчики отказов при успешной сессии
        microSessionRefusals = 0
        breakEndRefusals = 0
        
        Logger.shared.log(.info, "DailyWorkload", "✅ Сессия завершена: \(minutes) мин = \(points) очков. Всего: \(getTodayPoints())/\(currentPointsBar)")
        
        // Проверяем достижение цели
        if hasReachedDailyGoal() {
            showDailyGoalCompletedDialog()
        }
    }
    
    /// Обрабатывает отказ от микросессии
    func handleMicroSessionRefusal() {
        microSessionRefusals += 1
        lastRefusalTime = Date()

        Logger.shared.log(.info, "DailyWorkload", "❌ Отказ от микросессии #\(microSessionRefusals)")

        // ИСПРАВЛЕНО: диалог показываем только после 3-го отказа (когда счетчик = 4)
        if microSessionRefusals > 3 {
            showEndDayDialog()
        } else {
            // Планируем следующее предложение с увеличенным интервалом
            let baseInterval: TimeInterval = 30 * 60 * testModeMultiplier
            let multiplier = getRefusalMultiplier(refusals: microSessionRefusals)
            let nextInterval = baseInterval * multiplier

            Logger.shared.log(.info, "DailyWorkload", "📅 Отказ #\(microSessionRefusals): базовый интервал \(Int(baseInterval/60)) мин × \(multiplier) = \(Int(nextInterval/60)) мин")
            scheduleNextMicroSessionOffer(after: nextInterval)
        }
    }

    /// Обрабатывает отказ от мягкого предложения (ФИНАЛЬНЫЙ отказ на весь день)
    func handleGentleOfferRefusal() {
        Logger.shared.log(.info, "DailyWorkload", "🛑 ФИНАЛЬНЫЙ отказ от мягкого предложения - больше не беспокоим сегодня")

        // Сбрасываем все счетчики и отменяем все запланированные предложения
        microSessionRefusals = 0
        breakEndRefusals = 0

        // Отменяем все запланированные таймеры
        cancelAllScheduledOffers()

        Logger.shared.log(.info, "DailyWorkload", "✅ Все предложения отменены до конца дня")
    }

    /// Отменяет все запланированные предложения (заглушка)
    private func cancelAllScheduledOffers() {
        // TODO: В будущем здесь можно сохранять ссылки на Timer и отменять их
        // Пока просто логируем что отмена запрошена
        Logger.shared.log(.info, "DailyWorkload", "🚫 Запрошена отмена всех запланированных предложений")
    }

    /// Обрабатывает отказ от BreakEndWindow (кнопка "Later")
    func handleBreakEndRefusal() {
        breakEndRefusals += 1
        lastRefusalTime = Date()
        
        Logger.shared.log(.info, "DailyWorkload", "❌ Отказ от BreakEnd #\(breakEndRefusals)")
        
        // ИСПРАВЛЕНО: Общий счетчик отказов для диалога "хватит на сегодня"
        // Диалог показываем только после 3-х отказов (когда счетчик = 4)
        let totalRefusals = microSessionRefusals + breakEndRefusals
        Logger.shared.log(.info, "DailyWorkload", "📊 Общий счетчик отказов: micro=\(microSessionRefusals) + breakEnd=\(breakEndRefusals) = \(totalRefusals)")

        if totalRefusals > 3 {
            showEndDayDialog()
        }
    }
    
    /// Проверяет достигнута ли дневная цель
    func hasReachedDailyGoal() -> Bool {
        return getTodayPoints() >= currentPointsBar
    }
    
    /// Возвращает прогресс к дневной цели (0.0 - 1.0+)
    func getDailyProgress() -> Double {
        return Double(getTodayPoints()) / Double(currentPointsBar)
    }
    
    /// Возвращает текущую планку очков
    func getCurrentPointsBar() -> Int {
        return currentPointsBar
    }
    
    /// Возвращает очки за сегодня
    func getTodayPoints() -> Int {
        return UserDefaults.standard.integer(forKey: "todayPoints_\(todayDateString())")
    }
    
    /// Возвращает текущий уровень пользователя
    func getCurrentUserLevel() -> UserLevel {
        return userLevel
    }

    /// ОТЛАДКА: Сбрасывает очки за сегодня
    func resetTodayPointsForTesting() {
        let key = "todayPoints_\(todayDateString())"
        UserDefaults.standard.removeObject(forKey: key)

        // Также сбрасываем отказы для полного сброса
        microSessionRefusals = 0
        breakEndRefusals = 0

        Logger.shared.log(.info, "DailyWorkload", "🔄 ОТЛАДКА: Очки и отказы сброшены")
    }

    // MARK: - Private Methods

    private func updateUserLevel() {
        let daysWithoutWork = getDaysWithoutWork()
        let avgSessionLength = getAverageSessionLength(lastDays: 7)

        if daysWithoutWork >= 3 {
            userLevel = .beginner
        } else if avgSessionLength >= 30 {
            userLevel = .regular
        } else {
            userLevel = .starter
        }

        Logger.shared.log(.info, "DailyWorkload", "👤 Уровень пользователя: \(userLevel.displayName)")
    }

    private func getInitialPointsBar(level: UserLevel) -> Int {
        switch level {
        case .beginner: return 30
        case .starter: return 60
        case .regular: return 120
        case .advanced: return 200
        }
    }

    private func calculateContextBonus(minutes: Int, level: UserLevel) -> Int {
        switch level {
        case .beginner:
            if minutes <= 5 { return minutes * 2 }
            if minutes <= 15 { return minutes * 1 }
            return 0

        case .starter:
            if minutes <= 5 { return minutes * 1 }
            if minutes <= 25 { return minutes / 2 }
            return minutes / 3

        case .regular:
            if minutes <= 5 { return 0 }
            if minutes >= 25 { return minutes / 2 }
            return 0

        case .advanced:
            if minutes >= 30 { return minutes / 2 }
            return 0
        }
    }

    private func isWithinWorkingHours() -> Bool {
        let hour = Calendar.current.component(.hour, from: Date())
        return hour >= workingHours.start && hour < workingHours.end
    }

    private func getTimeOfDayMultiplier() -> Double {
        let hour = Calendar.current.component(.hour, from: Date())

        if hour < 12 {
            return 0.8  // Утром чаще
        } else if hour > 15 {
            return 1.3  // Вечером реже
        } else {
            return 1.0  // Днем обычно
        }
    }

    private func getRefusalMultiplier(refusals: Int) -> Double {
        switch refusals {
        case 0: return 1.0  // 30 мин
        case 1: return 2.0  // 60 мин
        case 2: return 3.0  // 90 мин
        default: return 3.0
        }
    }

    private func getProgressMultiplier() -> Double {
        let progress = getDailyProgress()

        if progress >= 1.0 {
            return 4.0  // Очень редко после достижения цели
        } else if progress >= 0.8 {
            return 2.0  // Реже когда близко к цели
        } else {
            return 1.0  // Обычно
        }
    }

    private func shouldOfferBonusSession() -> Bool {
        // Одно предложение после обеда максимум
        let hour = Calendar.current.component(.hour, from: Date())
        let wasOffered = UserDefaults.standard.bool(forKey: "postLunchOfferMade_\(todayDateString())")
        return hour >= 13 && hour <= 15 && !wasOffered
    }

    private func addTodayPoints(_ points: Int) {
        let key = "todayPoints_\(todayDateString())"
        let current = UserDefaults.standard.integer(forKey: key)
        UserDefaults.standard.set(current + points, forKey: key)
    }

    private func todayDateString() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: Date())
    }

    private func resetDailyCountersIfNeeded() {
        let lastResetDate = UserDefaults.standard.string(forKey: "lastDailyReset")
        let today = todayDateString()

        if lastResetDate != today {
            UserDefaults.standard.set(today, forKey: "lastDailyReset")
            UserDefaults.standard.removeObject(forKey: "postLunchOfferMade_\(today)")

            // Адаптируем планку на основе вчерашних результатов
            adaptPointsBarBasedOnYesterday()

            Logger.shared.log(.info, "DailyWorkload", "🔄 Новый день. Счетчики сброшены")
        }
    }

    private func adaptPointsBarBasedOnYesterday() {
        // Получаем очки за вчера
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: Date())!
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        let yesterdayString = formatter.string(from: yesterday)
        let yesterdayPoints = UserDefaults.standard.integer(forKey: "todayPoints_\(yesterdayString)")

        // Адаптируем планку
        let success = yesterdayPoints >= currentPointsBar

        if success {
            currentPointsBar = Int(Double(currentPointsBar) * 1.15)
        } else {
            currentPointsBar = Int(Double(currentPointsBar) * 0.85)
        }

        // Ограничения по уровню
        let minPoints = getInitialPointsBar(level: userLevel)
        let maxPoints = minPoints * 4
        currentPointsBar = max(minPoints, min(maxPoints, currentPointsBar))

        Logger.shared.log(.info, "DailyWorkload", "📊 Планка адаптирована: \(currentPointsBar) очков (вчера: \(yesterdayPoints))")
    }

    private func showEndDayDialog() {
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "Может хватит на сегодня?"
            alert.informativeText = "Вы уже несколько раз отказались. Завершить рабочий день?"
            alert.addButton(withTitle: "Да, хватит")
            alert.addButton(withTitle: "Нет, просто чуть позже")
            alert.alertStyle = .informational

            let response = alert.runModal()

            if response == .alertFirstButtonReturn {
                // "Да, хватит" - планируем мягкое предложение через 3 часа
                self.scheduleGentleAfternoonOffer()
                Logger.shared.log(.info, "DailyWorkload", "🛑 Пользователь завершил день. Мягкое предложение запланировано")
            } else {
                // "Нет, просто чуть позже" - продолжаем с максимальным интервалом БЕЗ мягкого предложения

                // ИСПРАВЛЕНО: Сбрасываем счетчик отказов, чтобы диалог не показывался снова
                self.microSessionRefusals = 0
                self.breakEndRefusals = 0
                Logger.shared.log(.info, "DailyWorkload", "🔄 Счетчики отказов сброшены")

                self.scheduleNextMicroSessionOffer(after: 90 * 60 * self.testModeMultiplier)
                // НЕ планируем мягкое предложение - пользователь хочет продолжать работать
                Logger.shared.log(.info, "DailyWorkload", "⏰ Пользователь просит позже. Интервал увеличен до 90 мин")
            }
        }
    }

    // MARK: - Заглушки для методов которые будут реализованы позже

    private func getDaysWithoutWork() -> Int {
        // TODO: Реализовать подсчет дней без работы
        return 0
    }

    private func getAverageSessionLength(lastDays: Int) -> Double {
        // TODO: Реализовать подсчет средней длительности сессий
        return 20.0
    }

    private func showDailyGoalCompletedDialog() {
        DispatchQueue.main.async {
            DailyGoalCompletedWindow.show(
                currentPoints: self.getTodayPoints(),
                targetPoints: self.currentPointsBar,
                onContinue: {
                    Logger.shared.log(.info, "DailyWorkload", "🔄 Пользователь продолжает работу после достижения цели")
                    // Помечаем что послеобеденное предложение было сделано
                    UserDefaults.standard.set(true, forKey: "postLunchOfferMade_\(self.todayDateString())")
                },
                onDone: { feedback in
                    Logger.shared.log(.info, "DailyWorkload", "✅ Пользователь завершил день. Обратная связь: \(feedback.isEmpty ? "нет" : "есть")")
                    // Больше не предлагаем работу сегодня
                    UserDefaults.standard.set(true, forKey: "workDayCompleted_\(self.todayDateString())")
                }
            )
        }
        Logger.shared.log(.info, "DailyWorkload", "🎯 Цель достигнута! Показываем DailyGoalCompletedWindow")
    }

    /// Планирует ПЕРВОЕ предложение микросессии после завершения сессии
    func scheduleFirstMicroSessionOffer(after interval: TimeInterval) {
        Logger.shared.log(.info, "DailyWorkload", "📅 Планируем ПЕРВОЕ предложение микросессии через \(Int(interval/60)) мин (\(Int(interval)) сек)")

        // Планируем первое предложение микросессии через AppDelegate
        DispatchQueue.main.async {
            Timer.scheduledTimer(withTimeInterval: interval, repeats: false) { _ in
                Logger.shared.log(.info, "DailyWorkload", "⏰ Время для ПЕРВОГО предложения микросессии!")

                // Проверяем можно ли предложить
                guard DailyWorkloadManager.shared.shouldOfferSession() else {
                    Logger.shared.log(.info, "DailyWorkload", "❌ Первое предложение отменено - условия не выполнены")
                    return
                }

                // Показываем MicroSessionOfferWindow напрямую через AppDelegate
                if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
                    Logger.shared.log(.info, "DailyWorkload", "💬 Показываем ПЕРВОЕ MicroSessionOfferWindow")
                    appDelegate.showMicroSessionOffer()
                } else {
                    Logger.shared.log(.error, "DailyWorkload", "❌ Не удалось получить AppDelegate для первого предложения")
                }
            }
        }
        Logger.shared.log(.info, "DailyWorkload", "⏰ Запланировано ПЕРВОЕ предложение через \(Int(interval/60)) мин")
    }

    /// Планирует ПОСЛЕДУЮЩИЕ предложения микросессии после отказов
    private func scheduleNextMicroSessionOffer(after interval: TimeInterval) {
        Logger.shared.log(.info, "DailyWorkload", "📅 Планируем ПОСЛЕДУЮЩЕЕ предложение микросессии через \(Int(interval/60)) мин (\(Int(interval)) сек)")

        // Планируем последующее предложение микросессии через AppDelegate
        DispatchQueue.main.async {
            Timer.scheduledTimer(withTimeInterval: interval, repeats: false) { _ in
                Logger.shared.log(.info, "DailyWorkload", "⏰ Время для ПОСЛЕДУЮЩЕГО предложения микросессии!")

                // Проверяем можно ли предложить
                guard DailyWorkloadManager.shared.shouldOfferSession() else {
                    Logger.shared.log(.info, "DailyWorkload", "❌ Последующее предложение отменено - условия не выполнены")
                    return
                }

                // Показываем MicroSessionOfferWindow напрямую через AppDelegate
                if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
                    Logger.shared.log(.info, "DailyWorkload", "💬 Показываем ПОСЛЕДУЮЩЕЕ MicroSessionOfferWindow")
                    appDelegate.showMicroSessionOffer()
                } else {
                    Logger.shared.log(.error, "DailyWorkload", "❌ Не удалось получить AppDelegate для последующего предложения")
                }
            }
        }
        Logger.shared.log(.info, "DailyWorkload", "⏰ Запланировано ПОСЛЕДУЮЩЕЕ предложение через \(Int(interval/60)) мин")
    }

    private func scheduleGentleAfternoonOffer() {
        let currentHour = Calendar.current.component(.hour, from: Date())
        let workingHoursEnd = workingHours.end

        // ИСПРАВЛЕНО: Применяем тестовый режим (3 часа → 3 минуты в тестовом режиме)
        let baseInterval: TimeInterval = 3 * 60 * 60 // 3 часа
        let interval = baseInterval * testModeMultiplier

        Logger.shared.log(.info, "DailyWorkload", "🌅 Планируем мягкое предложение через \(Int(interval/60)) мин (\(Int(interval)) сек)")

        // В тестовом режиме не проверяем время до конца дня
        if testModeMultiplier >= 1.0 {
            // Обычный режим - проверяем что есть время до конца рабочего дня
            guard currentHour + 3 < workingHoursEnd else {
                Logger.shared.log(.info, "DailyWorkload", "🌅 Мягкое предложение не запланировано - мало времени до конца дня")
                return
            }
        }

        DispatchQueue.main.async {
            Timer.scheduledTimer(withTimeInterval: interval, repeats: false) { _ in
                Logger.shared.log(.info, "DailyWorkload", "🌅 Время для мягкого предложения!")

                // ИСПРАВЛЕНО: Мягкие предложения показываются ВСЕГДА, независимо от количества отказов
                // Это специальное предложение после "хватит на сегодня"
                Logger.shared.log(.info, "DailyWorkload", "🌅 Показываем мягкое предложение (без проверки условий)")

                // Показываем GentleAfternoonOfferWindow
                DispatchQueue.main.async {
                    // ИСПРАВЛЕНО: Получаем позицию статус-бара для правильного позиционирования
                    var statusItemFrame = NSRect.zero
                    if let appDelegate = NSApplication.shared.delegate as? AppDelegate,
                       let statusButton = appDelegate.statusItem.button,
                       let statusWindow = statusButton.window {
                        let buttonFrame = statusButton.convert(statusButton.bounds, to: nil)
                        statusItemFrame = statusWindow.convertToScreen(buttonFrame)
                    }

                    GentleAfternoonOfferWindow.show(
                        statusItemFrame: statusItemFrame,
                        onAccept: { projectId in
                            Logger.shared.log(.info, "DailyWorkload", "✅ Пользователь принял мягкое предложение")
                            // Запускаем сессию через AppDelegate
                            if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
                                let projectIdString = projectId?.uuidString
                                appDelegate.startSessionFromGentleOffer(projectId: projectIdString)
                            }
                        },
                        onDecline: {
                            Logger.shared.log(.info, "DailyWorkload", "❌ Пользователь отклонил мягкое предложение")
                            // Обработка отказа уже в declineButtonClicked
                        }
                    )
                }
            }
        }
        Logger.shared.log(.info, "DailyWorkload", "🌅 Запланировано мягкое предложение через 3 часа")
    }

    // MARK: - Тестирование и отладка

    /// Получить информацию о текущих очках (для тестирования)
    func getPointsInfo() -> String {
        let todayPoints = getTodayPoints()
        let currentBar = getCurrentPointsBar()
        let level = userLevel.displayName
        let refusals = microSessionRefusals

        return """
        📊 Очки DailyWorkloadManager:
        • Сегодня: \(todayPoints) очков
        • Планка: \(currentBar) очков
        • Уровень: \(level)
        • Отказы: \(refusals)/3
        • Тест режим: \(isTestMode ? "ВКЛ (30 сек)" : "ВЫКЛ (30 мин)")
        """
    }

    /// Логирует текущую информацию об очках
    func logPointsInfo() {
        Logger.shared.log(.info, "DailyWorkload", getPointsInfo())
    }
}
