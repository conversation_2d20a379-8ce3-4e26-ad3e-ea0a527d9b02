import Cocoa

/// Окно обратной связи при достижении дневной цели очков
class DailyGoalCompletedWindow: NSWindow {
    
    // MARK: - UI Elements
    private var containerView: NSView!
    private var titleLabel: NSTextField!
    private var subtitleLabel: NSTextField!
    private var progressLabel: NSTextField!
    private var continueButton: NSButton!
    private var doneButton: NSButton!

    // Система оценки дня
    private var ratingStackView: NSStackView!
    private var ratingButtons: [HoverButton] = []
    internal var selectedRating: Int = 0
    private var easyLabel: NSTextField!
    private var hardLabel: NSTextField!
    
    // MARK: - Callbacks
    var onContinue: (() -> Void)?
    var onDone: ((String) -> Void)?
    
    // MARK: - Properties
    private var currentPoints: Int = 0
    private var targetPoints: Int = 0
    
    // MARK: - Initialization
    
    init() {
        super.init(contentRect: NSRect(x: 0, y: 0, width: 420, height: 240),
                   styleMask: [.borderless],
                   backing: .buffered,
                   defer: false)

        self.isOpaque = false
        self.backgroundColor = NSColor.clear
        self.level = .floating
        self.hasShadow = true
        self.isMovableByWindowBackground = true
        self.center()

        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        // Создаем основной контейнер
        containerView = NSView()
        containerView.translatesAutoresizingMaskIntoConstraints = false

        // Настраиваем унифицированный фон (тип "сессия")
        self.setupUnifiedBackground(type: .session, for: containerView)

        self.contentView = containerView

        // Создаем элементы UI
        createUIElements()
        setupConstraints()

        Logger.shared.log(.info, "DailyGoalCompleted", "🪟 Окно создано")
    }
    
    private func createUIElements() {
        // Заголовок
        titleLabel = NSTextField.createUnifiedTitle(text: "🎯 Дневная цель достигнута!")
        titleLabel.font = NSFont.systemFont(ofSize: 18, weight: .bold)

        // Подзаголовок
        subtitleLabel = NSTextField.createUnifiedSubtitle(text: "Насколько сегодня было легко/тяжело?")
        subtitleLabel.font = NSFont.systemFont(ofSize: 14, weight: .regular)

        // Прогресс
        progressLabel = NSTextField.createUnifiedSubtitle(text: "")
        progressLabel.font = NSFont.systemFont(ofSize: 12, weight: .regular)
        progressLabel.textColor = NSColor(red: 0.4, green: 0.8, blue: 0.4, alpha: 1.0)

        // Система оценки дня
        setupRatingSystem()
        
        // Кнопки
        continueButton = NSButton.createDashedButton(title: "Продолжить работу", type: UIButtonType.purple)
        continueButton.target = self
        continueButton.action = #selector(continueButtonClicked)

        doneButton = NSButton.createUnifiedButton(title: "Завершить день", type: UIButtonType.green, isSmall: false)
        doneButton.target = self
        doneButton.action = #selector(doneButtonClicked)
        
        // Добавляем элементы в контейнер
        containerView.addSubview(titleLabel)
        containerView.addSubview(progressLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(easyLabel)
        containerView.addSubview(ratingStackView)
        containerView.addSubview(hardLabel)
        containerView.addSubview(continueButton)
        containerView.addSubview(doneButton)
    }
    


    private func setupRatingSystem() {
        // Подписи "Легко" и "Тяжело"
        easyLabel = NSTextField.createUnifiedSubtitle(text: "Легко")
        easyLabel.font = NSFont.systemFont(ofSize: 11, weight: .medium)
        easyLabel.textColor = NSColor.white

        hardLabel = NSTextField.createUnifiedSubtitle(text: "Тяжело")
        hardLabel.font = NSFont.systemFont(ofSize: 11, weight: .medium)
        hardLabel.textColor = NSColor.white

        // Создаем горизонтальный стек для кнопок оценки
        ratingStackView = NSStackView()
        ratingStackView.orientation = .horizontal
        ratingStackView.distribution = .fillEqually
        ratingStackView.spacing = 12
        ratingStackView.translatesAutoresizingMaskIntoConstraints = false

        // Цвета для градиента от зеленого к красному
        let ratingColors = [
            NSColor(red: 0.4, green: 0.8, blue: 0.4, alpha: 1.0),  // Зеленый (легко)
            NSColor(red: 0.6, green: 0.8, blue: 0.3, alpha: 1.0),  // Желто-зеленый
            NSColor(red: 0.9, green: 0.7, blue: 0.2, alpha: 1.0),  // Желто-оранжевый (средне)
            NSColor(red: 0.9, green: 0.5, blue: 0.2, alpha: 1.0),  // Оранжево-красный
            NSColor(red: 0.9, green: 0.3, blue: 0.3, alpha: 1.0)   // Красный (тяжело)
        ]

        // Создаем 5 цветных кружков
        for index in 0..<5 {
            let button = HoverButton(baseColor: ratingColors[index])
            button.tag = index + 1
            button.target = self
            button.action = #selector(ratingButtonClicked(_:))

            button.translatesAutoresizingMaskIntoConstraints = false
            button.widthAnchor.constraint(equalToConstant: 36).isActive = true
            button.heightAnchor.constraint(equalToConstant: 36).isActive = true

            ratingButtons.append(button)
            ratingStackView.addArrangedSubview(button)
        }
    }


    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // Заголовок
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 20),
            titleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            titleLabel.leadingAnchor.constraint(greaterThanOrEqualTo: containerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor, constant: -20),

            // Прогресс (зеленый текст с очками)
            progressLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 12),
            progressLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),

            // Подзаголовок (вопрос о легкости/тяжести)
            subtitleLabel.topAnchor.constraint(equalTo: progressLabel.bottomAnchor, constant: 25),
            subtitleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            subtitleLabel.leadingAnchor.constraint(greaterThanOrEqualTo: containerView.leadingAnchor, constant: 20),
            subtitleLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor, constant: -20),

            // Кнопки оценки
            ratingStackView.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 20),
            ratingStackView.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            ratingStackView.heightAnchor.constraint(equalToConstant: 36),

            // Подпись "Легко" - слева от кнопок, выровнена по центру
            easyLabel.centerYAnchor.constraint(equalTo: ratingStackView.centerYAnchor),
            easyLabel.trailingAnchor.constraint(equalTo: ratingStackView.leadingAnchor, constant: -15),

            // Подпись "Тяжело" - справа от кнопок, выровнена по центру
            hardLabel.centerYAnchor.constraint(equalTo: ratingStackView.centerYAnchor),
            hardLabel.leadingAnchor.constraint(equalTo: ratingStackView.trailingAnchor, constant: 15),

            // Кнопки (поменяли местами: "Завершить день" слева, "Продолжить работу" справа)
            doneButton.topAnchor.constraint(equalTo: ratingStackView.bottomAnchor, constant: 25),
            doneButton.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 25),
            doneButton.heightAnchor.constraint(equalToConstant: 32),
            doneButton.widthAnchor.constraint(equalToConstant: 160),
            doneButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -20),

            continueButton.topAnchor.constraint(equalTo: ratingStackView.bottomAnchor, constant: 25),
            continueButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -25),
            continueButton.heightAnchor.constraint(equalToConstant: 32),
            continueButton.widthAnchor.constraint(equalToConstant: 160),
            continueButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -20)
        ])
    }
    
    // MARK: - Public Methods
    
    /// Показывает окно с информацией о достижении цели
    func showGoalCompleted(currentPoints: Int, targetPoints: Int) {
        self.currentPoints = currentPoints
        self.targetPoints = targetPoints

        updateProgressInfo()
        self.showWithUnifiedAnimation()

        Logger.shared.log(.info, "DailyGoalCompleted", "🎯 Показано окно достижения цели: \(currentPoints)/\(targetPoints)")
    }
    
    private func updateProgressInfo() {
        let percentage = Int((Double(currentPoints) / Double(targetPoints)) * 100)
        progressLabel.stringValue = "Выполнено: \(currentPoints) из \(targetPoints) очков (\(percentage)%)"
    }
    
    // MARK: - Actions
    
    @objc private func continueButtonClicked() {
        Logger.shared.log(.info, "DailyGoalCompleted", "🔄 Пользователь выбрал продолжить работу")

        self.hideWithUnifiedAnimation { [weak self] in
            self?.onContinue?()
        }

        // Сохраняем оценку дня
        if selectedRating > 0 {
            saveDayRating(selectedRating)
        }
    }
    
    @objc private func doneButtonClicked() {
        Logger.shared.log(.info, "DailyGoalCompleted", "✅ Пользователь завершил день")

        self.hideWithUnifiedAnimation { [weak self] in
            self?.onDone?("")  // Передаем пустую строку вместо feedback
        }

        // Сохраняем оценку дня
        if selectedRating > 0 {
            saveDayRating(selectedRating)
        }
    }

    @objc private func ratingButtonClicked(_ sender: NSButton) {
        let rating = sender.tag
        selectedRating = rating

        // Обновляем визуальное состояние кнопок
        updateRatingButtonsAppearance()

        Logger.shared.log(.info, "DailyGoalCompleted", "⭐ Пользователь оценил день: \(rating)/5")
    }

    private func updateRatingButtonsAppearance() {
        for (index, button) in ratingButtons.enumerated() {
            let rating = index + 1
            // Используем новый метод setSelected для красивых анимаций
            button.setSelected(rating <= selectedRating)
        }
    }
    
    // MARK: - Helper Methods
    
    private func saveDayRating(_ rating: Int) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let today = dateFormatter.string(from: Date())

        UserDefaults.standard.set(rating, forKey: "dayRating_\(today)")
        Logger.shared.log(.info, "DailyGoalCompleted", "⭐ Оценка дня сохранена: \(rating)/5")
    }
    


    // MARK: - Keyboard Support

    override func keyDown(with event: NSEvent) {
        switch event.keyCode {
        case 36: // Enter
            if event.modifierFlags.contains(.command) {
                doneButtonClicked()
            }
        case 53: // Escape
            doneButtonClicked()
        default:
            super.keyDown(with: event)
        }
    }
}

// MARK: - Custom Hover Button

class HoverButton: NSButton {
    private let baseColor: NSColor

    init(baseColor: NSColor) {
        self.baseColor = baseColor
        super.init(frame: .zero)
        self.wantsLayer = true
        self.title = ""
        self.isBordered = false
        setupInitialAppearance()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupInitialAppearance() {
        // Настраиваем начальный вид
        layer?.cornerRadius = 18
        layer?.backgroundColor = baseColor.withAlphaComponent(0.3).cgColor
        layer?.borderWidth = 2
        layer?.borderColor = baseColor.withAlphaComponent(0.6).cgColor
    }

    func setSelected(_ selected: Bool) {
        if selected {
            // Выбранное состояние - простое без анимаций
            layer?.backgroundColor = baseColor.withAlphaComponent(0.9).cgColor
            layer?.borderColor = baseColor.cgColor
            layer?.borderWidth = 3
        } else {
            // Невыбранное состояние
            layer?.backgroundColor = baseColor.withAlphaComponent(0.3).cgColor
            layer?.borderColor = baseColor.withAlphaComponent(0.6).cgColor
            layer?.borderWidth = 2
        }
    }
}

// MARK: - Static Factory Method

extension DailyGoalCompletedWindow {

    /// Создает и показывает окно достижения дневной цели
    static func show(currentPoints: Int,
                     targetPoints: Int,
                     onContinue: @escaping () -> Void,
                     onDone: @escaping (String) -> Void) {
        let window = DailyGoalCompletedWindow()
        window.onContinue = onContinue
        window.onDone = onDone
        window.showGoalCompleted(currentPoints: currentPoints, targetPoints: targetPoints)
    }
}
