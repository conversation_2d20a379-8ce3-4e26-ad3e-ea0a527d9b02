import Cocoa

// MARK: - Типы градиентов для окон
enum WindowGradientType {
    case session    // Для окон завершения сессии (синие/фиолетовые)
    case rest       // Для окон завершения отдыха (зеленые/изумрудные)
}

// MARK: - Типы кнопок
enum UIButtonType {
    case green      // Основные действия (Start, Continue, Yes)
    case gray       // Второстепенные действия (Later, No, Cancel)
    case purple     // Специальные действия с обводкой (Postpone)
}

// MARK: - Унифицированная система стилей
struct UIStyles {
    
    // MARK: - Цвета градиентов
    
    /// Основной градиент для фона окон
    static func getMainGradientColors() -> [CGColor] {
        return [
            NSColor(red: 0.1, green: 0.1, blue: 0.15, alpha: 0.95).cgColor,
            NSColor(red: 0.15, green: 0.15, blue: 0.2, alpha: 0.95).cgColor
        ]
    }
    
    /// Радиальные градиенты в зависимости от типа окна
    static func getRadialGradientColors(type: WindowGradientType) -> [(colors: [CGColor], alpha: Float)] {
        switch type {
        case .session:
            // Синие/фиолетовые для окон сессии
            return [
                (colors: [
                    NSColor(red: 0.3, green: 0.4, blue: 0.8, alpha: 0.3).cgColor,
                    NSColor.clear.cgColor
                ], alpha: 0.3),
                (colors: [
                    NSColor(red: 0.8, green: 0.3, blue: 0.6, alpha: 0.2).cgColor,
                    NSColor.clear.cgColor
                ], alpha: 0.2)
            ]
            
        case .rest:
            // Зеленые/изумрудные для окон отдыха
            return [
                (colors: [
                    NSColor(red: 0.4, green: 0.7, blue: 0.3, alpha: 0.3).cgColor,
                    NSColor.clear.cgColor
                ], alpha: 0.3),
                (colors: [
                    NSColor(red: 0.2, green: 0.6, blue: 0.4, alpha: 0.2).cgColor,
                    NSColor.clear.cgColor
                ], alpha: 0.2)
            ]
        }
    }
    
    // MARK: - Цвета кнопок
    
    static func getButtonColors(type: UIButtonType) -> [CGColor] {
        switch type {
        case .green:
            return [
                NSColor(red: 0.2, green: 0.8, blue: 0.3, alpha: 1.0).cgColor,
                NSColor(red: 0.1, green: 0.6, blue: 0.2, alpha: 1.0).cgColor
            ]
        case .gray:
            return [
                NSColor(red: 0.4, green: 0.4, blue: 0.4, alpha: 1.0).cgColor,
                NSColor(red: 0.25, green: 0.25, blue: 0.25, alpha: 1.0).cgColor
            ]
        case .purple:
            return [
                NSColor(red: 0.7, green: 0.3, blue: 1.0, alpha: 1.0).cgColor,
                NSColor(red: 0.5, green: 0.2, blue: 0.8, alpha: 1.0).cgColor
            ]
        }
    }
    
    // MARK: - Размеры и параметры
    
    static let cornerRadius: CGFloat = 12
    static let buttonCornerRadius: CGFloat = 8
    static let buttonHeight: CGFloat = 32
    static let smallButtonHeight: CGFloat = 24
    
    // MARK: - Шрифты
    
    static let titleFont = NSFont.systemFont(ofSize: 15, weight: .bold)
    static let subtitleFont = NSFont.systemFont(ofSize: 12, weight: .regular)
    static let buttonFont = NSFont.systemFont(ofSize: 13, weight: .medium)
    static let smallButtonFont = NSFont.systemFont(ofSize: 11, weight: .medium)
    
    // MARK: - Цвета текста
    
    static let titleTextColor = NSColor.white
    static let subtitleTextColor = NSColor(white: 0.8, alpha: 1.0)
    static let buttonTextColor = NSColor.white
}

// MARK: - Extensions для NSWindow

extension NSWindow {
    
    /// Настраивает унифицированный фон окна
    func setupUnifiedBackground(type: WindowGradientType, for view: NSView) {
        view.wantsLayer = true
        
        // Основной градиентный слой
        let mainGradient = CAGradientLayer()
        mainGradient.colors = UIStyles.getMainGradientColors()
        mainGradient.startPoint = CGPoint(x: 0, y: 0)
        mainGradient.endPoint = CGPoint(x: 1, y: 1)
        mainGradient.cornerRadius = UIStyles.cornerRadius
        
        view.layer?.insertSublayer(mainGradient, at: 0)
        
        // Обновляем размер при изменении view
        DispatchQueue.main.async {
            mainGradient.frame = view.bounds
            self.addRadialGradients(to: view, type: type)
        }
    }
    
    /// Добавляет радиальные градиенты в зависимости от типа окна
    private func addRadialGradients(to view: NSView, type: WindowGradientType) {
        guard let layer = view.layer else { return }
        
        let radialColors = UIStyles.getRadialGradientColors(type: type)
        
        // Первый радиальный градиент (левый верхний)
        let radial1 = CAGradientLayer()
        radial1.type = .radial
        radial1.colors = radialColors[0].colors
        radial1.startPoint = CGPoint(x: 0.2, y: 0.8)
        radial1.endPoint = CGPoint(x: 0.8, y: 0.2)
        radial1.frame = layer.bounds
        layer.addSublayer(radial1)
        
        // Второй радиальный градиент (правый нижний)
        let radial2 = CAGradientLayer()
        radial2.type = .radial
        radial2.colors = radialColors[1].colors
        radial2.startPoint = CGPoint(x: 0.8, y: 0.2)
        radial2.endPoint = CGPoint(x: 0.2, y: 0.8)
        radial2.frame = layer.bounds
        layer.addSublayer(radial2)
    }
    
    /// Показывает окно с анимацией
    func showWithUnifiedAnimation() {
        self.alphaValue = 0.0
        self.makeKeyAndOrderFront(nil)
        
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)
            self.animator().alphaValue = 1.0
        }
    }
    
    /// Скрывает окно с анимацией
    func hideWithUnifiedAnimation(completion: (() -> Void)? = nil) {
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.2
            context.timingFunction = CAMediaTimingFunction(name: .easeIn)
            self.animator().alphaValue = 0.0
        }, completionHandler: {
            self.orderOut(nil)
            completion?()
        })
    }
}

// MARK: - Extensions для NSButton

extension NSButton {
    
    /// Создает унифицированную кнопку
    static func createUnifiedButton(title: String, type: UIButtonType, isSmall: Bool = false) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isBordered = false
        button.wantsLayer = true
        
        // Создаем градиентный слой
        let gradientLayer = CAGradientLayer()
        switch type {
        case .green:
            gradientLayer.colors = UIStyles.getButtonColors(type: .green)
        case .gray:
            gradientLayer.colors = UIStyles.getButtonColors(type: .gray)
        case .purple:
            gradientLayer.colors = UIStyles.getButtonColors(type: .purple)
        }
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        gradientLayer.cornerRadius = UIStyles.buttonCornerRadius
        
        button.layer?.insertSublayer(gradientLayer, at: 0)
        
        // Настройка текста
        button.font = isSmall ? UIStyles.smallButtonFont : UIStyles.buttonFont
        button.contentTintColor = UIStyles.buttonTextColor
        
        // Обновляем размер градиента при изменении кнопки
        DispatchQueue.main.async {
            gradientLayer.frame = button.bounds
        }
        
        return button
    }
    
    /// Создает кнопку с пунктирной обводкой (для фиолетовых кнопок)
    static func createDashedButton(title: String, type: UIButtonType) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isBordered = false
        button.wantsLayer = true

        // Создаем основной слой
        let containerLayer = CALayer()
        containerLayer.backgroundColor = NSColor.clear.cgColor
        button.layer = containerLayer

        // Создаем слой с пунктирной обводкой
        let borderLayer = CAShapeLayer()
        borderLayer.fillColor = NSColor.clear.cgColor
        borderLayer.lineWidth = 1.0
        borderLayer.lineDashPattern = [3, 2] // Пунктирная линия: 3px линия, 2px пробел

        // Цвета в зависимости от типа
        let color: NSColor
        switch type {
        case .purple:
            color = NSColor(red: 0.7, green: 0.6, blue: 0.8, alpha: 1.0) // Приглушенный фиолетовый
        case .green:
            color = NSColor(red: 0.2, green: 0.8, blue: 0.3, alpha: 1.0)
        case .gray:
            color = NSColor(red: 0.6, green: 0.6, blue: 0.6, alpha: 1.0)
        }

        borderLayer.strokeColor = color.withAlphaComponent(0.7).cgColor
        containerLayer.addSublayer(borderLayer)

        // Обновляем путь при изменении размера
        DispatchQueue.main.async {
            let bounds = button.bounds
            let path = NSBezierPath(roundedRect: bounds, xRadius: 6, yRadius: 6)
            borderLayer.path = path.cgPath
            borderLayer.frame = bounds
        }

        // Цветной текст
        button.attributedTitle = NSAttributedString(
            string: title,
            attributes: [
                .foregroundColor: color.withAlphaComponent(0.8),
                .font: NSFont.systemFont(ofSize: 11, weight: .medium)
            ]
        )

        return button
    }
}

// MARK: - Extensions для NSTextField

extension NSTextField {
    
    /// Создает унифицированный заголовок
    static func createUnifiedTitle(text: String) -> NSTextField {
        let label = NSTextField(labelWithString: text)
        label.font = UIStyles.titleFont
        label.textColor = UIStyles.titleTextColor
        label.alignment = .center
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }
    
    /// Создает унифицированный подзаголовок
    static func createUnifiedSubtitle(text: String) -> NSTextField {
        let label = NSTextField(labelWithString: text)
        label.font = UIStyles.subtitleFont
        label.textColor = UIStyles.subtitleTextColor
        label.alignment = .center
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }
}
