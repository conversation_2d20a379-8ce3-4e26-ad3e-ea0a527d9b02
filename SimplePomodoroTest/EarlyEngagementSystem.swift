import Foundation
import Cocoa

// MARK: - Supporting Data Structures for Streak System

/// Запись о работе в конкретный день
struct WorkDay: Codable {
    let date: Date
    let workedOnPriorityProject: Bool

    /// Создает запись для сегодняшнего дня
    static func today(worked: Bool) -> WorkDay {
        return WorkDay(
            date: Calendar.current.startOfDay(for: Date()),
            workedOnPriorityProject: worked
        )
    }
}

/// Уровень вовлечения с поддержкой streak
enum EngagementLevel {
    case workingToday(streakDays: Int)  // Новый! Заменяет старый уровень 0
    case oneDayOff                      // Старый уровень 1
    case twoDaysOff                     // Старый уровень 2-3 (2-3 дня)
    case weekOff                        // Старый уровень 3 (4-6 дней)
    case criticalOff                    // Старый уровень 4 (7+ дней)

    /// Преобразует в старый индекс матрицы для совместимости
    var matrixIndex: Int {
        switch self {
        case .workingToday(_):
            return 0
        case .oneDayOff:
            return 1
        case .twoDaysOff:
            return 2
        case .weekOff:
            return 3
        case .criticalOff:
            return 4
        }
    }
}

/// Система раннего вовлечения - двумерная эскалация для помощи пользователям начинать работу утром
/// Использует адаптивные планки и принцип "минимального плана"
class EarlyEngagementSystem {
    
    // MARK: - Singleton
    static let shared = EarlyEngagementSystem()
    private init() {
        loadUserBarData()
        loadEngagementHistory()
        loadWorkHistory()  // Загружаем историю работы для streak
        Logger.shared.log(.info, "EarlyEngagement", "🌅 EarlyEngagementSystem инициализирован")
    }
    
    // MARK: - Properties
    
    /// Текущее состояние системы
    private var isActive = false
    
    /// Текущая планка пользователя (адаптивная)
    private var currentUserBar: TimeInterval = 52 * 60 // Начальная планка 52 минуты
    
    /// История изменений планки для адаптации
    private var userBarHistory: [UserBarEntry] = []
    
    /// Дни без работы (для ВЕРТИКАЛЬНОЙ эскалации)
    private var daysWithoutWork: Int = 0
    
    /// Время последней работы
    private var lastWorkTime: Date?
    
    /// История событий вовлечения
    private var engagementHistory: [EngagementEvent] = []
    
    /// Флаг сброса эскалации после первого успешного интервала
    private var escalationResetAfterSuccess = false

    // НОВОЕ: Свойства для отслеживания активности после пробуждения
    private var waitingForActivity = false
    private var pendingSleepDuration: TimeInterval = 0
    private var pendingWakeTime: Date?
    private var activityCheckTimer: Timer?
    private let activityTracker = MinuteActivityTracker()

    // НОВОЕ: Свойства для системы повторных показов
    private var todayWakeUpTime: Date?      // Время пробуждения сегодня
    private var firstMessageShownTime: Date? // Время показа первого сообщения
    private var repeatTimers: [Timer] = []  // Таймеры для повторных показов (20мин, 1ч, 2ч)
    private var shownMessageIndexes: Set<Int> = []  // Какие сообщения уже показали сегодня

    // MARK: - Streak System Properties

    /// История работы по дням для подсчета streak
    private var workHistory: [WorkDay] = []

    /// Текущий streak (непрерывные дни работы)
    private var currentStreak: Int = 0

    /// Максимальное количество дней для хранения истории
    private let maxWorkHistoryDays = 100

    // MARK: - Dependencies
    
    /// Ссылка на ProjectManager для получения приоритетного проекта
    weak var projectManager: ProjectManager?
    
    /// Ссылка на PomodoroTimer для запуска интервалов
    weak var pomodoroTimer: PomodoroTimer?
    
    /// Ссылка на SleepWakeDetector для получения событий пробуждения
    weak var sleepWakeDetector: SleepWakeDetector?
    
    // MARK: - Delegates & Callbacks
    
    /// Делегат для показа UI сообщений
    weak var delegate: EarlyEngagementSystemDelegate?
    
    /// Колбэк для показа окна раннего вовлечения
    var onShowEngagementWindow: ((EngagementMessage) -> Void)?
    
    /// Колбэк для записи статистики
    var onRecordStatistics: ((EngagementEvent) -> Void)?
    
    // MARK: - Public Methods
    
    /// Запускает систему раннего вовлечения
    func start() {
        guard !isActive else {
            Logger.shared.log(.info, "EarlyEngagement", "🌅 Система уже активна")
            return
        }

        isActive = true
        Logger.shared.log(.info, "EarlyEngagement", "🌅 Система раннего вовлечения запущена")

        // Загружаем данные о последней работе
        loadLastWorkTime()
        _ = calculateDaysWithoutWork()

        // Адаптируем планку по дням без работы
        adaptUserBarByDaysWithoutWork()

        Logger.shared.log(.info, "EarlyEngagement", "📊 Текущая планка: \(Int(currentUserBar/60)) мин, дней без работы: \(daysWithoutWork)")
    }
    
    /// Останавливает систему
    func stop() {
        isActive = false
        stopWaitingForActivity() // Останавливаем ожидание активности
        clearAllTimers() // НОВОЕ: Очищаем таймеры повторных показов
        Logger.shared.log(.info, "EarlyEngagement", "🌅 Система раннего вовлечения остановлена")
    }
    
    /// Обрабатывает событие пробуждения компьютера
    func handleWakeUpEvent(sleepDuration: TimeInterval, wakeTime: Date) {
        guard isActive else { return }

        Logger.shared.log(.info, "EarlyEngagement", "🌅 Обработка пробуждения: сон \(Int(sleepDuration/60)) мин")

        // ИСПРАВЛЕНО: Используем новую систему ожидания активности вместо прямого показа
        // Это обеспечит запуск системы повторных показов
        startWaitingForActivity(sleepDuration: sleepDuration, wakeTime: wakeTime)
    }

    /// НОВОЕ: Начинает ожидание активности пользователя после пробуждения
    func startWaitingForActivity(sleepDuration: TimeInterval, wakeTime: Date) {
        guard isActive else { return }

        Logger.shared.log(.info, "EarlyEngagement", "🌅 Начинаем ожидание активности после пробуждения")

        // Проверяем базовые условия (время дня, длительность сна, не показывали сегодня)
        guard shouldShowEngagementMessage(wakeTime: wakeTime, sleepDuration: sleepDuration) else {
            Logger.shared.log(.info, "EarlyEngagement", "❌ Условия для показа не выполнены")
            return
        }

        // НОВОЕ: Сохраняем время пробуждения для системы повторных показов
        todayWakeUpTime = wakeTime
        clearOldTimers() // Очищаем старые таймеры
        shownMessageIndexes.removeAll() // Сбрасываем показанные сообщения
        firstMessageShownTime = nil // Сбрасываем время первого показа

        // Сохраняем параметры для последующего показа
        waitingForActivity = true
        pendingSleepDuration = sleepDuration
        pendingWakeTime = wakeTime

        // Запускаем отслеживание активности (проверяем каждые 3 секунды)
        activityTracker.startTracking()
        activityCheckTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { [weak self] _ in
            self?.checkForUserActivity()
        }

        Logger.shared.log(.info, "EarlyEngagement", "⏱️ Запущено отслеживание активности (проверка каждые 3 сек)")
    }

    /// Проверяет активность пользователя и показывает сообщение при обнаружении
    private func checkForUserActivity() {
        guard waitingForActivity else { return }

        // Используем 20-сегментную систему для проверки активности
        let isActive = activityTracker.wasCurrentMinuteActive()

        if isActive {
            Logger.shared.log(.info, "EarlyEngagement", "🎯 Обнаружена активность пользователя! Показываем сообщение через 1 секунду")

            // Останавливаем отслеживание
            stopWaitingForActivity()

            // Показываем первое сообщение через 1 секунду после активности
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                guard let self = self else { return }

                // Показываем первое сообщение (message_1)
                self.showMessageByIndex(0)

                // НОВОЕ: Запускаем систему повторных показов
                self.scheduleRepeatMessages()
            }
        }
    }

    /// Останавливает ожидание активности
    private func stopWaitingForActivity() {
        waitingForActivity = false
        activityCheckTimer?.invalidate()
        activityCheckTimer = nil
        activityTracker.stopTracking()

        Logger.shared.log(.info, "EarlyEngagement", "⏹️ Остановлено ожидание активности")
    }

    // MARK: - Система повторных показов

    /// Запускает таймеры для повторных показов (20мин, 1ч, 2ч)
    private func scheduleRepeatMessages() {
        guard todayWakeUpTime != nil else { return }

        // ИСПРАВЛЕНИЕ: Сохраняем время первого показа для правильного расчета
        firstMessageShownTime = Date()

        Logger.shared.log(.info, "EarlyEngagement", "⏰ Запускаем таймеры повторных показов от времени первого показа")

        // Таймер на 20 минут (message_2)
        let timer20min = Timer.scheduledTimer(withTimeInterval: 20 * 60, repeats: false) { [weak self] _ in
            self?.checkAndShowMessage(index: 1, description: "20 минут")
        }

        // Таймер на 1 час (message_3)
        let timer1hour = Timer.scheduledTimer(withTimeInterval: 60 * 60, repeats: false) { [weak self] _ in
            self?.checkAndShowMessage(index: 2, description: "1 час")
        }

        // Таймер на 2 часа (message_4)
        let timer2hours = Timer.scheduledTimer(withTimeInterval: 120 * 60, repeats: false) { [weak self] _ in
            self?.checkAndShowMessage(index: 3, description: "2 часа")
        }

        repeatTimers = [timer20min, timer1hour, timer2hours]
        Logger.shared.log(.info, "EarlyEngagement", "✅ Запущено 3 таймера: 20мин, 1ч, 2ч")
    }

    /// Проверяет условия и показывает сообщение по индексу
    private func checkAndShowMessage(index: Int, description: String) {
        Logger.shared.log(.info, "EarlyEngagement", "⏰ Сработал таймер: \(description) (message_\(index + 1))")

        // Проверяем условия остановки
        guard shouldContinueShowing() else {
            Logger.shared.log(.info, "EarlyEngagement", "🛑 Остановка показов: пользователь уже работает")
            clearAllTimers()
            return
        }

        // Проверяем не ночное ли время
        guard !isNightTime() else {
            Logger.shared.log(.info, "EarlyEngagement", "🌙 Ночное время - не показываем сообщение")
            return
        }

        // Показываем сообщение
        showMessageByIndex(index)
    }

    /// Показывает сообщение по индексу (0=message_1, 1=message_2, etc.)
    private func showMessageByIndex(_ index: Int) {
        // Проверяем не показывали ли уже это сообщение
        guard !shownMessageIndexes.contains(index) else {
            Logger.shared.log(.info, "EarlyEngagement", "⚠️ Сообщение \(index) уже показывали")
            return
        }

        Logger.shared.log(.info, "EarlyEngagement", "💬 Показываем сообщение по индексу: \(index)")

        // Отмечаем как показанное
        shownMessageIndexes.insert(index)

        // Создаем сообщение с принудительным горизонтальным уровнем
        _debugHorizontalLevel = index
        let message = createEngagementMessage()
        _debugHorizontalLevel = nil // Сбрасываем отладочный уровень

        // Показываем сообщение
        showEngagementMessageDirect(message)
    }

    /// Проверяет должны ли мы продолжать показывать сообщения
    private func shouldContinueShowing() -> Bool {
        // 1. Проверяем работал ли уже сегодня над приоритетным проектом (полноценная сессия)
        if workedOnPriorityProjectToday() {
            Logger.shared.log(.info, "EarlyEngagement", "✅ Уже работал над приоритетным проектом сегодня")
            return false
        }

        // 2. Проверяем активна ли сейчас сессия по приоритетному проекту
        if isWorkingOnPriorityProject() {
            Logger.shared.log(.info, "EarlyEngagement", "🔄 Сейчас работает над приоритетным проектом")
            return false
        }

        return true
    }

    /// Проверяет ночное ли время (23:00 - 5:00)
    private func isNightTime() -> Bool {
        let hour = Calendar.current.component(.hour, from: Date())
        return hour >= 23 || hour <= 5
    }

    /// Проверяет работал ли сегодня над приоритетным проектом (полноценная сессия ≥ планки)
    private func workedOnPriorityProjectToday() -> Bool {
        guard let focusedProject = getFocusedProject() else { return false }

        // TODO: Проверить завершенные сегодня интервалы по приоритетному проекту
        // Сессия считается полноценной, если duration >= currentUserBar

        Logger.shared.log(.info, "EarlyEngagement", "🔍 Проверка работы над приоритетным проектом: \(focusedProject.name)")
        return false // Пока заглушка
    }

    /// Проверяет активна ли сейчас сессия по приоритетному проекту
    private func isWorkingOnPriorityProject() -> Bool {
        guard let focusedProject = getFocusedProject(),
              let timer = pomodoroTimer else { return false }

        // Проверяем активен ли таймер и совпадает ли проект
        let isTimerActive = timer.state == .working
        let currentProjectId = (NSApplication.shared.delegate as? AppDelegate)?.currentProjectId
        let isCorrectProject = currentProjectId == focusedProject.id

        Logger.shared.log(.info, "EarlyEngagement", "🔍 Активная сессия: таймер=\(isTimerActive), проект=\(isCorrectProject)")
        return isTimerActive && isCorrectProject
    }

    /// Очищает старые таймеры
    private func clearOldTimers() {
        clearAllTimers()
    }

    /// Очищает все активные таймеры
    private func clearAllTimers() {
        for timer in repeatTimers {
            timer.invalidate()
        }
        repeatTimers.removeAll()
        Logger.shared.log(.info, "EarlyEngagement", "🗑️ Очищены все таймеры повторных показов")
    }

    /// Показывает сообщение напрямую (без дополнительных проверок)
    private func showEngagementMessageDirect(_ message: EngagementMessage) {
        let enhancedMessage = enhanceMessageWithFullSession(message)

        Logger.shared.log(.info, "EarlyEngagement", "💬 Показываем сообщение: \(enhancedMessage.title)")

        // Сохраняем дату показа
        saveMessageShownToday()

        // Записываем событие показа
        recordEngagementEvent(.messageShown, duration: currentUserBar)

        // Показываем через делегат
        delegate?.showEngagementMessage(
            enhancedMessage,
            onAccept: { [weak self] projectId in
                self?.handleUserAcceptance(projectId: projectId)
                self?.clearAllTimers() // Останавливаем повторные показы
            },
            onDecline: { [weak self] in
                self?.handleUserDecline()
                // НЕ останавливаем таймеры - продолжаем показывать
            },
            onSnooze: { [weak self] in
                self?.handleUserSnooze(proposedDuration: enhancedMessage.proposedDuration)
                // НЕ останавливаем таймеры - продолжаем показывать
            },
            onFullSession: { [weak self] projectId in
                self?.handleFullSessionAcceptance(projectId: projectId, context: enhancedMessage.sessionContext)
                self?.clearAllTimers() // Останавливаем повторные показы
            }
        )
        onShowEngagementWindow?(enhancedMessage)
    }
    
    /// Обрабатывает успешное завершение интервала
    func handleIntervalCompletion(duration: TimeInterval, projectId: UUID?) {
        Logger.shared.log(.info, "EarlyEngagement", "✅ Интервал завершен: \(Int(duration/60)) мин")

        // УДАЛЕНО: DailyWorkloadManager.handleSessionCompleted теперь вызывается в AppDelegate
        // чтобы избежать дублирования для микросессий

        // Обновляем время последней работы только если это приоритетный проект
        let focusedProject = getFocusedProject()
        let workedOnPriorityProject = projectId != nil && focusedProject?.id == projectId

        Logger.shared.log(.info, "EarlyEngagement", "🔍 ОТЛАДКА handleIntervalCompletion:")
        Logger.shared.log(.info, "EarlyEngagement", "  projectId: \(projectId?.uuidString ?? "nil")")
        Logger.shared.log(.info, "EarlyEngagement", "  focusedProject: \(focusedProject?.name ?? "nil") (\(focusedProject?.id.uuidString ?? "nil"))")
        Logger.shared.log(.info, "EarlyEngagement", "  workedOnPriorityProject: \(workedOnPriorityProject)")

        if workedOnPriorityProject {
            lastWorkTime = Date()
            daysWithoutWork = 0
            Logger.shared.log(.info, "EarlyEngagement", "📅 Работа над приоритетным проектом зафиксирована")
        } else {
            Logger.shared.log(.info, "EarlyEngagement", "📅 Работа не над приоритетным проектом, дни без работы не сбрасываются")
        }

        // Обновляем историю работы для streak-системы
        updateWorkHistoryForToday(worked: workedOnPriorityProject)

        // Сбрасываем эскалацию после первого успешного интервала
        if !escalationResetAfterSuccess {
            escalationResetAfterSuccess = true
            Logger.shared.log(.info, "EarlyEngagement", "🔄 Эскалация сброшена после первого успешного интервала")
        }
        
        // Адаптируем планку на основе успеха
        adaptUserBar(success: true, intervalDuration: duration)

        // Записываем в статистику
        EngagementStatistics.shared.recordIntervalCompleted(
            duration: duration,
            projectId: projectId,
            wasFromEngagement: true
        )

        // Сохраняем данные
        saveUserBarData()
        saveLastWorkTime()

        // Записываем событие
        recordEngagementEvent(.intervalCompleted, duration: duration, projectId: projectId)
    }
    
    /// Обрабатывает отказ пользователя от интервала
    func handleUserRefusal(proposedDuration: TimeInterval) {
        Logger.shared.log(.info, "EarlyEngagement", "❌ Пользователь отказался от интервала \(Int(proposedDuration/60)) мин")

        // Записываем в статистику
        let (vertical, horizontal) = getCurrentMatrixPosition()
        EngagementStatistics.shared.recordUserRefusal(
            vertical: vertical,
            horizontal: horizontal,
            userBar: currentUserBar
        )

        // Адаптируем планку на основе отказа
        adaptUserBar(success: false, intervalDuration: proposedDuration)
        
        // Записываем событие
        recordEngagementEvent(.userRefused, duration: proposedDuration)
        
        saveUserBarData()
    }

    /// Обрабатывает отклонение пользователем предложения
    func handleUserDecline() {
        Logger.shared.log(.info, "EarlyEngagement", "❌ Пользователь отклонил предложение")

        // Записываем в статистику
        let (vertical, horizontal) = getCurrentMatrixPosition()
        EngagementStatistics.shared.recordUserRefusal(
            vertical: vertical,
            horizontal: horizontal,
            userBar: currentUserBar
        )

        // Адаптируем планку (уменьшаем при отказе)
        adaptUserBar(success: false, intervalDuration: currentUserBar)

        saveUserBarData()
    }

    /// Обрабатывает отложение пользователем предложения
    func handleUserSnooze(proposedDuration: TimeInterval) {
        Logger.shared.log(.info, "EarlyEngagement", "⏰ Пользователь отложил интервал \(Int(proposedDuration/60)) мин")

        // Записываем в статистику
        let (vertical, horizontal) = getCurrentMatrixPosition()
        EngagementStatistics.shared.recordUserSnooze(
            vertical: vertical,
            horizontal: horizontal,
            userBar: currentUserBar
        )

        // Записываем событие отложения
        recordEngagementEvent(.userSnoozed, duration: proposedDuration)
    }

    /// Возвращает текущую планку пользователя
    func getCurrentUserBar() -> TimeInterval {
        return currentUserBar
    }

    /// Возвращает текущую планку пользователя в минутах
    func getCurrentUserBarMinutes() -> Int {
        return Int(currentUserBar / 60)
    }

    /// Обновляет текущую планку пользователя (для системы взращивания)
    func updateUserBar(_ newBar: TimeInterval) {
        let oldBar = currentUserBar
        currentUserBar = newBar

        // Записываем изменение в историю
        let entry = UserBarEntry(
            timestamp: Date(),
            oldValue: oldBar,
            newValue: newBar,
            reason: .sessionGrowth,
            intervalDuration: 0 // Для роста через сессию не важно
        )
        userBarHistory.append(entry)

        // Сохраняем данные
        saveUserBarData()

        logInfo("EarlyEngagement", "📊 Планка обновлена: \(Int(oldBar/60)) → \(Int(newBar/60)) мин")
    }
    
    /// Возвращает приоритетный проект для подстановки в сообщения
    func getFocusedProject() -> Project? {
        return projectManager?.getPriorityProject()
    }

    /// Запускает интервал с адаптивной длительностью
    func startAdaptiveInterval(projectId: UUID? = nil) {
        guard let timer = pomodoroTimer else {
            Logger.shared.log(.info, "EarlyEngagement", "❌ PomodoroTimer не установлен")
            return
        }

        // Устанавливаем адаптивную длительность из текущей планки
        let adaptiveDuration = currentUserBar
        timer.updateWorkDuration(adaptiveDuration)

        Logger.shared.log(.info, "EarlyEngagement", "🚀 Запуск адаптивного интервала: \(Int(adaptiveDuration/60)) мин")

        // Запускаем интервал
        timer.startInterval()

        // Записываем событие принятия пользователем
        recordEngagementEvent(.userAccepted, duration: adaptiveDuration, projectId: projectId)

        // Если есть проект, отмечаем его как использованный
        if let projectId = projectId {
            projectManager?.markProjectAsUsed(projectId)
        }
    }

    /// Обрабатывает согласие пользователя на интервал
    func handleUserAcceptance(projectId: UUID? = nil) {
        Logger.shared.log(.info, "EarlyEngagement", "✅ Пользователь согласился на интервал")

        // Записываем в статистику
        let (vertical, horizontal) = getCurrentMatrixPosition()
        EngagementStatistics.shared.recordUserAcceptance(
            vertical: vertical,
            horizontal: horizontal,
            userBar: currentUserBar,
            projectId: projectId
        )

        startAdaptiveInterval(projectId: projectId)
    }

    /// Обрабатывает согласие пользователя на полную сессию (52 минуты)
    func handleFullSessionAcceptance(projectId: UUID? = nil, context: FullSessionContext) {
        Logger.shared.log(.info, "EarlyEngagement", "🌟 Пользователь выбрал полную сессию (52 мин)")

        // Регистрируем попытку полной сессии
        FullSessionSystem.shared.recordFullSessionAttempt()

        // Записываем в статистику раннего вовлечения
        let (vertical, horizontal) = getCurrentMatrixPosition()
        EngagementStatistics.shared.recordUserAcceptance(
            vertical: vertical,
            horizontal: horizontal,
            userBar: 52 * 60, // Полная сессия
            projectId: projectId
        )

        // Запускаем полную сессию
        startFullSession(projectId: projectId, context: context)
    }

    /// Запускает полную сессию (52 минуты)
    private func startFullSession(projectId: UUID? = nil, context: FullSessionContext) {
        guard let timer = pomodoroTimer else {
            Logger.shared.log(.info, "EarlyEngagement", "❌ PomodoroTimer не установлен")
            return
        }

        let fullSessionDuration: TimeInterval = 52 * 60

        // Устанавливаем колбэк для отслеживания завершения полной сессии
        let originalCallback = timer.onFullIntervalCompleted
        timer.onFullIntervalCompleted = { [weak self] duration in
            // Вызываем оригинальный колбэк
            originalCallback?(duration)

            // Обрабатываем завершение полной сессии
            self?.handleFullSessionCompletion(duration: duration, context: context, projectId: projectId)
        }

        // Запускаем таймер на 52 минуты
        timer.updateWorkDuration(fullSessionDuration)
        timer.startInterval()

        // Устанавливаем проект если указан
        if let projectId = projectId {
            projectManager?.markProjectAsUsed(projectId)
        }

        Logger.shared.log(.info, "EarlyEngagement", "🚀 Запущена полная сессия 52 минуты")
    }

    /// Обрабатывает завершение полной сессии
    private func handleFullSessionCompletion(duration: TimeInterval, context: FullSessionContext, projectId: UUID?) {
        let fullSessionDuration: TimeInterval = 52 * 60

        if duration >= fullSessionDuration * 0.9 { // Считаем успешным если выполнено >= 90%
            Logger.shared.log(.info, "EarlyEngagement", "✅ Полная сессия успешно завершена")

            // НОВОЕ: Уведомляем DailyWorkloadManager о завершении полной сессии
            DailyWorkloadManager.shared.handleSessionCompleted(minutes: Int(duration / 60))

            // Регистрируем успешное завершение
            FullSessionSystem.shared.recordFullSessionCompletion(context: convertToFullSessionContext(context))

            // Обновляем планку пользователя
            let newBar = FullSessionSystem.shared.calculateNewBarAfterFullSession(context: convertToFullSessionContext(context))
            currentUserBar = newBar

            // Сбрасываем эскалацию
            escalationResetAfterSuccess = true

        } else {
            Logger.shared.log(.info, "EarlyEngagement", "❌ Полная сессия не завершена")

            // Регистрируем неудачу
            FullSessionSystem.shared.recordFullSessionFailure()
        }

        saveUserBarData()
    }

    /// Конвертирует контекст из EarlyEngagement в FullSession
    private func convertToFullSessionContext(_ context: FullSessionContext) -> FullSessionSystem.SessionContext {
        let originalBar = currentUserBar

        switch context {
        case .horizontalDescaling:
            return .horizontalDescaling(originalBar: originalBar)
        case .verticalDegradation:
            return .verticalDegradation(originalBar: originalBar)
        case .restCompletion:
            return .restCompletion(currentBar: originalBar)
        case .earlyEngagement:
            return .earlyEngagement(currentBar: originalBar)
        }
    }

    /// Настраивает обратную связь с PomodoroTimer
    func setupPomodoroTimerCallbacks() {
        guard let timer = pomodoroTimer else {
            Logger.shared.log(.info, "EarlyEngagement", "❌ PomodoroTimer не установлен для настройки колбэков")
            return
        }

        // Сохраняем существующий колбэк если есть
        let existingCallback = timer.onFullIntervalCompleted

        // Устанавливаем новый колбэк который вызывает и старый и наш
        timer.onFullIntervalCompleted = { [weak self] duration in
            // Вызываем существующий колбэк
            existingCallback?(duration)

            // ИСПРАВЛЕНО: Получаем реальный projectId из AppDelegate
            let currentProjectId = (NSApplication.shared.delegate as? AppDelegate)?.currentProjectId

            // Обрабатываем завершение интервала в системе раннего вовлечения
            self?.handleIntervalCompletion(duration: duration, projectId: currentProjectId)
        }

        Logger.shared.log(.info, "EarlyEngagement", "🔗 Колбэки PomodoroTimer настроены")
    }
    
    // MARK: - Private Methods
    
    /// Определяет нужно ли показывать сообщение раннего вовлечения
    private func shouldShowEngagementMessage(wakeTime: Date, sleepDuration: TimeInterval) -> Bool {
        // Проверяем время дня (утренние часы предпочтительнее)
        let hour = Calendar.current.component(.hour, from: wakeTime)
        let isGoodTime = hour >= 6 && hour <= 12

        // Проверяем длительность сна (реальный сон vs короткий перерыв)
        let isRealSleep = sleepDuration > 30 * 60 // Больше 30 минут

        // НОВОЕ: Проверяем показывали ли уже сегодня
        let alreadyShownToday = wasMessageShownToday()

        // НОВОЕ: Проверяем через DailyWorkloadManager
        let workloadAllows = DailyWorkloadManager.shared.shouldOfferSession()

        // НОВОЕ: В тестовом режиме игнорируем ограничение "один раз в день"
        let isTestMode = UserDefaults.standard.bool(forKey: "DailyWorkloadFastTestMode")
        let shouldIgnoreAlreadyShown = isTestMode

        Logger.shared.log(.info, "EarlyEngagement", "🤔 Анализ: время=\(hour)ч (\(isGoodTime ? "хорошее" : "не очень")), сон=\(Int(sleepDuration/60))мин (\(isRealSleep ? "реальный" : "короткий")), уже показывали сегодня=\(alreadyShownToday), workload разрешает=\(workloadAllows), тестовый режим=\(isTestMode)")

        return isGoodTime && isRealSleep && (!alreadyShownToday || shouldIgnoreAlreadyShown) && workloadAllows
    }
    
    /// Показывает сообщение раннего вовлечения
    private func showEngagementMessage() {
        // Создаем сообщение на основе матрицы
        var message = createEngagementMessage()

        // Добавляем поддержку полной сессии
        message = enhanceMessageWithFullSession(message)

        Logger.shared.log(.info, "EarlyEngagement", "💬 Показываем сообщение: \(message.title), полная сессия: \(message.showFullSessionButton)")

        // НОВОЕ: Сохраняем дату показа (один раз в день)
        saveMessageShownToday()

        // Записываем событие показа
        recordEngagementEvent(.messageShown, duration: currentUserBar)

        // Записываем в статистику
        let (vertical, horizontal) = getCurrentMatrixPosition()
        EngagementStatistics.shared.recordMessageShown(
            vertical: vertical,
            horizontal: horizontal,
            userBar: currentUserBar,
            daysWithoutWork: daysWithoutWork
        )

        // Показываем через делегат или колбэк
        delegate?.showEngagementMessage(
            message,
            onAccept: { [weak self] projectId in
                self?.handleUserAcceptance(projectId: projectId)
            },
            onDecline: { [weak self] in
                self?.handleUserDecline()
            },
            onSnooze: { [weak self] in
                self?.handleUserSnooze(proposedDuration: message.proposedDuration)
            },
            onFullSession: { [weak self] projectId in
                self?.handleFullSessionAcceptance(projectId: projectId, context: message.sessionContext)
            }
        )
        onShowEngagementWindow?(message)
    }
    
    /// Создает сообщение на основе матрицы 5x4
    func createEngagementMessage() -> EngagementMessage {
        Logger.shared.log(.info, "EarlyEngagement", "🔄 ВХОД В createEngagementMessage()")

        // НОВАЯ ЛОГИКА: Определяем режим работы (streak или дни без работы)
        let actualDaysWithoutWork: Int
        // let verticalLevel: Int  // Unused
        let isStreakMode: Bool

        if let debugStreak = _debugStreakDays {
            // Режим streak - используем уровень 0 (работал сегодня)
            actualDaysWithoutWork = 0
            // verticalLevel = 0  // Unused
            isStreakMode = true
            Logger.shared.log(.info, "EarlyEngagement", "🔥 Используем отладочный streak: \(debugStreak) дней")
        } else if let debugDays = _debugDaysWithoutWork {
            // Режим дней без работы (отладочный)
            actualDaysWithoutWork = debugDays
            // verticalLevel = getDaysLevelIndex(daysWithoutWork: debugDays)  // Unused
            isStreakMode = false
            Logger.shared.log(.info, "EarlyEngagement", "🔧 Используем отладочные дни без работы: \(debugDays)")
        } else {
            // Реальные данные - определяем режим по текущему состоянию
            actualDaysWithoutWork = daysWithoutWork
            // verticalLevel = getDaysLevelIndex(daysWithoutWork: daysWithoutWork)  // Unused
            isStreakMode = (daysWithoutWork == 0 && currentStreak > 0)
            Logger.shared.log(.info, "EarlyEngagement", "📊 Используем реальные данные: дни=\(daysWithoutWork), streak=\(currentStreak)")
        }

        // Определяем время дня (ГОРИЗОНТАЛЬ)
        let horizontalLevel: Int
        if let debugLevel = _debugHorizontalLevel {
            // Используем отладочный горизонтальный уровень
            horizontalLevel = debugLevel
            Logger.shared.log(.info, "EarlyEngagement", "🔧 Используем отладочный горизонтальный уровень: \(debugLevel)")
        } else {
            // Используем реальное время дня
            let hour = Calendar.current.component(.hour, from: Date())
            horizontalLevel = getTimeOfDayLevel(hour: hour)
            Logger.shared.log(.info, "EarlyEngagement", "📊 Используем реальное время дня: час=\(hour), уровень=\(horizontalLevel)")
        }

        // НОВАЯ СИСТЕМА: Определяем режим (streak или return)
        let baseMessage: EngagementMessage
        let effectiveStreakDays = _debugStreakDays ?? currentStreak
        if effectiveStreakDays > 0 {
            // Streak режим - используем новую матрицу
            Logger.shared.log(.info, "EarlyEngagement", "🔥 Используем STREAK режим: \(effectiveStreakDays) дней")
            baseMessage = NewMessageConstructionMatrix.getStreakMessage(streakDays: effectiveStreakDays, messageIndex: horizontalLevel)
        } else {
            // Return режим - используем новую матрицу
            let daysWithoutWork = _debugDaysWithoutWork ?? calculateDaysWithoutWork()
            Logger.shared.log(.info, "EarlyEngagement", "🔄 Используем RETURN режим: \(daysWithoutWork) дней без работы")
            baseMessage = NewMessageConstructionMatrix.getReturnMessage(daysWithoutWork: daysWithoutWork, messageIndex: horizontalLevel)
            Logger.shared.log(.info, "EarlyEngagement", "📝 Получили baseMessage.title: '\(baseMessage.title)'")
        }

        // Подставляем переменные
        var processedMessage = substituteVariables(in: baseMessage)

        // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Проверяем галочку "использовать отладочную планку"
        let initialBarMinutes: Int
        if let debugBar = debugInitialBar {
            // Используем отладочную планку (галочка включена)
            initialBarMinutes = debugBar
            Logger.shared.log(.info, "EarlyEngagement", "🔧 Используем отладочную планку: \(debugBar)мин")
        } else {
            // Используем реальную планку пользователя (галочка выключена)
            initialBarMinutes = Int(currentUserBar / 60)
            Logger.shared.log(.info, "EarlyEngagement", "📊 Используем реальную планку: \(initialBarMinutes)мин")
        }

        // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Используем прямой расчет с актуальными днями без работы
        let calculatedBarMinutes = calculateBarForRealWindow(
            initialBar: initialBarMinutes,
            daysWithoutWork: actualDaysWithoutWork, // Передаем актуальные дни (отладочные или реальные)
            messageIndex: horizontalLevel
        )

        Logger.shared.log(.info, "EarlyEngagement", "🔄 ИСПРАВЛЕНО: Используем currentUserBar как базовую планку для единообразия")
        Logger.shared.log(.info, "EarlyEngagement", "🔄 ПЕРЕД ButtonMatrix: actualDaysWithoutWork=\(actualDaysWithoutWork), horizontalLevel=\(horizontalLevel)")
        Logger.shared.log(.info, "EarlyEngagement", "🔄 ПЕРЕД ButtonMatrix: currentUserBar=\(Int(currentUserBar/60))мин, calculatedBar=\(calculatedBarMinutes)мин")
        Logger.shared.log(.info, "EarlyEngagement", "🔄 ПЕРЕД ButtonMatrix: старый buttonText='\(processedMessage.buttonText)'")

        // ✅ ПЕРЕДАЕМ STREAK В BUTTONMATRIX для использования реальной логики
        let streakForButtonMatrix: Int? = isStreakMode ? (_debugStreakDays ?? currentStreak) : nil

        let buttons = ButtonMatrix.generateButtonsForEarlyEngagement(
            daysWithoutWork: actualDaysWithoutWork, // ИСПРАВЛЕНО: используем актуальные дни (отладочные или реальные)
            messageIndex: horizontalLevel,
            baseBarMinutes: initialBarMinutes,
            streakDays: streakForButtonMatrix  // ✅ Передаем streak в реальный код!
        )

        Logger.shared.log(.info, "EarlyEngagement", "🔄 ButtonMatrix сгенерировал \(buttons.count) кнопок:")
        for (index, button) in buttons.enumerated() {
            Logger.shared.log(.info, "EarlyEngagement", "🔄   Кнопка \(index): '\(button.text)'")
        }

        // КРИТИЧЕСКИ ВАЖНО: Передаем кнопки в сообщение
        processedMessage.buttonMatrix = buttons

        // Обновляем текст основной кнопки (первая кнопка из ButtonMatrix)
        if let primaryButton = buttons.first {
            let oldText = processedMessage.buttonText
            processedMessage.buttonText = primaryButton.text
            Logger.shared.log(.info, "EarlyEngagement", "🔄 ОБНОВЛЕН текст кнопки: '\(oldText)' → '\(primaryButton.text)'")
        } else {
            Logger.shared.log(.error, "EarlyEngagement", "🔄 ОШИБКА: ButtonMatrix не вернул ни одной кнопки!")
        }

        return processedMessage
    }

    /// Возвращает текущую позицию в матрице (вертикаль, горизонталь)
    private func getCurrentMatrixPosition() -> (vertical: Int, horizontal: Int) {
        let verticalLevel = min(daysWithoutWork, 4) // 0-4
        let hour = Calendar.current.component(.hour, from: Date())
        let horizontalLevel = getTimeOfDayLevel(hour: hour)
        return (verticalLevel, horizontalLevel)
    }

    /// Добавляет поддержку полной сессии к сообщению
    private func enhanceMessageWithFullSession(_ message: EngagementMessage) -> EngagementMessage {
        var enhancedMessage = message

        // Определяем контекст для системы полной сессии
        let context = determineSessionContext(message: message)
        enhancedMessage.sessionContext = context

        // Проверяем нужно ли показывать кнопку полной сессии
        enhancedMessage.showFullSessionButton = FullSessionSystem.shared.shouldShowFullSession(currentBar: message.proposedDuration)

        if enhancedMessage.showFullSessionButton {
            Logger.shared.log(.info, "EarlyEngagement", "✅ Добавлена кнопка полной сессии к сообщению")
        } else {
            Logger.shared.log(.info, "EarlyEngagement", "❌ Кнопка полной сессии не показана (планка=\(Int(message.proposedDuration/60))мин)")
        }

        return enhancedMessage
    }

    /// Определяет контекст сессии для системы полной сессии
    private func determineSessionContext(message: EngagementMessage) -> FullSessionContext {
        // Получаем исходную планку пользователя
        let originalBar = currentUserBar

        // Если предлагаемая длительность меньше исходной планки, это дескалация
        if message.proposedDuration < originalBar {
            // Проверяем тип дескалации
            if message.timeOfDay > 0 {
                // Горизонтальная дескалация (время дня)
                return .horizontalDescaling
            } else if message.level > 0 {
                // Вертикальная деградация (дни без работы)
                return .verticalDegradation
            }
        }

        // По умолчанию - раннее вовлечение
        return .earlyEngagement
    }

    /// Определяет уровень времени дня (0-3)
    private func getTimeOfDayLevel(hour: Int) -> Int {
        switch hour {
        case 6..<12: return 0  // Утро
        case 12..<18: return 1 // День
        case 18..<22: return 2 // Вечер
        default: return 3      // Ночь
        }
    }
    
    /// Подставляет переменные в сообщение
    func substituteVariables(in message: EngagementMessage) -> EngagementMessage {
        var processedMessage = message
        
        // Подставляем [focused_project]
        if let focusedProject = getFocusedProject() {
            processedMessage.title = processedMessage.title.replacingOccurrences(of: "[focused_project]", with: focusedProject.name)
            processedMessage.subtitle = processedMessage.subtitle.replacingOccurrences(of: "[focused_project]", with: focusedProject.name)
        } else {
            // Fallback для случая отсутствия приоритетного проекта
            processedMessage.title = processedMessage.title.replacingOccurrences(of: "[focused_project]", with: "важным проектом")
            processedMessage.subtitle = processedMessage.subtitle.replacingOccurrences(of: "[focused_project]", with: "важным проектом")
        }
        
        // Подставляем [current_bar]
        let currentBarMinutes = Int(currentUserBar / 60)
        processedMessage.title = processedMessage.title.replacingOccurrences(of: "[current_bar]", with: "\(currentBarMinutes)")
        processedMessage.subtitle = processedMessage.subtitle.replacingOccurrences(of: "[current_bar]", with: "\(currentBarMinutes)")

        // Подставляем [streak_days] для streak-системы
        let streakDays = _debugStreakDays ?? currentStreak
        processedMessage.title = processedMessage.title.replacingOccurrences(of: "[streak_days]", with: "\(streakDays)")
        processedMessage.subtitle = processedMessage.subtitle.replacingOccurrences(of: "[streak_days]", with: "\(streakDays)")

        // Обновляем предлагаемую длительность
        processedMessage.proposedDuration = currentUserBar

        return processedMessage
    }
    
    /// Адаптирует планку по дням без работы согласно документации
    private func adaptUserBarByDaysWithoutWork() {
        let oldBar = currentUserBar

        Logger.shared.log(.info, "EarlyEngagement", "🔍 ОТЛАДКА adaptUserBarByDaysWithoutWork: daysWithoutWork=\(daysWithoutWork), сохраненная планка=\(Int(oldBar/60))мин")

        switch daysWithoutWork {
        case 0:
            // Уровень 0 (работал вчера): планка остается неизменной при запуске
            // Градационный рост применяется только после успешного завершения интервала
            // currentUserBar остается без изменений
            Logger.shared.log(.info, "EarlyEngagement", "🔍 Случай 0: планка остается \(Int(currentUserBar/60))мин")
            break
        case 1:
            // ИСПРАВЛЕНО: Используем сохраненную планку, а не базовую 52 минуты
            // Уровень 1 (1 день пропуск): сохраненная планка × 0.77 (-23%)
            currentUserBar = oldBar * 0.77
            Logger.shared.log(.info, "EarlyEngagement", "🔍 Случай 1: планка = \(Int(oldBar/60)) × 0.77 = \(Int(currentUserBar/60))мин")
        case 2...3:
            // ИСПРАВЛЕНО: Используем сохраненную планку
            // Уровень 2-3 (2-3 дня): сохраненная планка × 0.48 (-52%)
            currentUserBar = oldBar * 0.48
            Logger.shared.log(.info, "EarlyEngagement", "🔍 Случай 2-3: планка = \(Int(oldBar/60)) × 0.48 = \(Int(currentUserBar/60))мин")
        case 4...6:
            // ИСПРАВЛЕНО: Используем сохраненную планку
            // Уровень 4-6 (4-6 дней): сохраненная планка × 0.29 (-71%)
            currentUserBar = oldBar * 0.29
            Logger.shared.log(.info, "EarlyEngagement", "🔍 Случай 4-6: планка = \(Int(oldBar/60)) × 0.29 = \(Int(currentUserBar/60))мин")
        default:
            // Уровень 7+ (неделя+): планка = 3 минуты (план-минимум согласно документации)
            currentUserBar = 3 * 60
            Logger.shared.log(.info, "EarlyEngagement", "🔍 Случай 7+: планка = 3мин")
        }

        Logger.shared.log(.info, "EarlyEngagement", "📊 Адаптация планки по дням: \(Int(oldBar/60))мин → \(Int(currentUserBar/60))мин (дней без работы: \(daysWithoutWork))")
    }

    /// Адаптирует планку пользователя на основе успеха/неудачи
    private func adaptUserBar(success: Bool, intervalDuration: TimeInterval) {
        let oldBar = currentUserBar

        if success {
            // При успехе используем градационную систему роста
            currentUserBar = GradualGrowthSystem.calculateGrowth(currentBar: currentUserBar)
            currentUserBar = min(currentUserBar, 52 * 60) // Максимум 52 минуты по документации
        } else {
            // При отказе уменьшаем планку на 15%
            currentUserBar = max(currentUserBar * 0.85, 2 * 60) // Минимум 2 минуты
        }
        
        // Записываем изменение в историю
        let entry = UserBarEntry(
            timestamp: Date(),
            oldValue: oldBar,
            newValue: currentUserBar,
            reason: success ? .success : .failure,
            intervalDuration: intervalDuration
        )
        userBarHistory.append(entry)
        
        // Ограничиваем размер истории
        if userBarHistory.count > 100 {
            userBarHistory.removeFirst()
        }
        
        Logger.shared.log(.info, "EarlyEngagement", "📊 Планка изменена: \(Int(oldBar/60))мин → \(Int(currentUserBar/60))мин (\(success ? "успех" : "неудача"))")
    }
    
    /// Вычисляет количество дней без работы
    private func calculateDaysWithoutWork() -> Int {
        let priorityProject = getFocusedProject()
        Logger.shared.log(.info, "EarlyEngagement", "🔍 ОТЛАДКА calculateDaysWithoutWork: приоритетный проект = \(priorityProject?.name ?? "НЕ ВЫБРАН")")

        guard let lastWork = lastWorkTime else {
            let result = 7 // Если нет данных, считаем максимальную эскалацию (7+ дней)
            daysWithoutWork = result
            Logger.shared.log(.info, "EarlyEngagement", "📅 Дней без работы: \(result) (никогда не работал над приоритетным проектом)")
            return result
        }

        // ИСПРАВЛЕНО: Правильный подсчет дней с учетом времени суток
        let calendar = Calendar.current
        let now = Date()
        let currentHour = calendar.component(.hour, from: now)

        // Получаем начало сегодняшнего дня
        let startOfToday = calendar.startOfDay(for: now)

        // Получаем день последней работы
        let startOfLastWorkDay = calendar.startOfDay(for: lastWork)

        // Считаем разность в днях
        let daysSince = calendar.dateComponents([.day], from: startOfLastWorkDay, to: startOfToday).day ?? 0

        // КЛЮЧЕВОЕ ИСПРАВЛЕНИЕ: Не штрафуем за текущий день до конца дня (23:59)
        let result: Int
        if daysSince == 1 {
            // Если прошел 1 день, но день еще не закончился - не штрафуем
            result = 0
            Logger.shared.log(.info, "EarlyEngagement", "📅 Дней без работы: \(result) (день еще не закончился, время: \(currentHour):xx)")
        } else {
            result = max(0, daysSince)
            Logger.shared.log(.info, "EarlyEngagement", "📅 Дней без работы: \(result) (последняя работа: \(lastWork), время: \(currentHour):xx)")
        }

        daysWithoutWork = result
        return result
    }

    /// Получает текущее время дня для горизонтальной эскалации
    private func getCurrentTimeOfDay() -> Int {
        let hour = Calendar.current.component(.hour, from: Date())

        // Утро (6-12): 0
        // День (12-18): 1
        // Вечер (18-22): 2
        // Ночь (22-6): 3
        switch hour {
        case 6..<12: return 0
        case 12..<18: return 1
        case 18..<22: return 2
        default: return 3
        }
    }

    /// Записывает событие вовлечения
    private func recordEngagementEvent(_ type: EngagementEventType, duration: TimeInterval, projectId: UUID? = nil) {
        let (vertical, horizontal) = getCurrentMatrixPosition()
        let event = EngagementEvent(
            type: type,
            timestamp: Date(),
            vertical: vertical,
            horizontal: horizontal,
            userBar: currentUserBar,
            daysWithoutWork: daysWithoutWork,
            projectId: projectId,
            intervalDuration: duration
        )
        
        engagementHistory.append(event)
        
        // Ограничиваем размер истории
        if engagementHistory.count > 1000 {
            engagementHistory.removeFirst()
        }
        
        // Уведомляем о событии
        onRecordStatistics?(event)
        
        saveEngagementHistory()
    }

    // MARK: - Data Persistence

    /// Загружает данные о планке пользователя
    private func loadUserBarData() {
        if let data = UserDefaults.standard.data(forKey: "earlyEngagement_userBar"),
           let decoded = try? JSONDecoder().decode(TimeInterval.self, from: data) {
            currentUserBar = decoded
            Logger.shared.log(.info, "EarlyEngagement", "📊 Загружена планка: \(Int(currentUserBar/60)) мин")
        }

        if let data = UserDefaults.standard.data(forKey: "earlyEngagement_userBarHistory"),
           let decoded = try? JSONDecoder().decode([UserBarEntry].self, from: data) {
            userBarHistory = decoded
            Logger.shared.log(.info, "EarlyEngagement", "📊 Загружена история планки: \(userBarHistory.count) записей")
        }
    }

    /// Сохраняет данные о планке пользователя
    private func saveUserBarData() {
        if let encoded = try? JSONEncoder().encode(currentUserBar) {
            UserDefaults.standard.set(encoded, forKey: "earlyEngagement_userBar")
        }

        if let encoded = try? JSONEncoder().encode(userBarHistory) {
            UserDefaults.standard.set(encoded, forKey: "earlyEngagement_userBarHistory")
        }
    }

    /// Загружает историю событий вовлечения
    private func loadEngagementHistory() {
        if let data = UserDefaults.standard.data(forKey: "earlyEngagement_history"),
           let decoded = try? JSONDecoder().decode([EngagementEvent].self, from: data) {
            engagementHistory = decoded
            Logger.shared.log(.info, "EarlyEngagement", "📊 Загружена история событий: \(engagementHistory.count) записей")
        }
    }

    /// Сохраняет историю событий вовлечения
    private func saveEngagementHistory() {
        if let encoded = try? JSONEncoder().encode(engagementHistory) {
            UserDefaults.standard.set(encoded, forKey: "earlyEngagement_history")
        }
    }

    /// Загружает время последней работы
    private func loadLastWorkTime() {
        if let timestamp = UserDefaults.standard.object(forKey: "earlyEngagement_lastWork") as? Date {
            lastWorkTime = timestamp
            Logger.shared.log(.info, "EarlyEngagement", "📅 Загружено время последней работы: \(timestamp)")
        }
    }

    /// Сохраняет время последней работы
    private func saveLastWorkTime() {
        if let lastWork = lastWorkTime {
            UserDefaults.standard.set(lastWork, forKey: "earlyEngagement_lastWork")
        }
    }

    /// Проверяет показывали ли сообщение уже сегодня
    private func wasMessageShownToday() -> Bool {
        guard let lastShownDate = UserDefaults.standard.object(forKey: "earlyEngagement_lastShown") as? Date else {
            return false // Никогда не показывали
        }

        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let lastShownDay = calendar.startOfDay(for: lastShownDate)

        return today == lastShownDay
    }

    /// Сохраняет дату показа сообщения
    private func saveMessageShownToday() {
        UserDefaults.standard.set(Date(), forKey: "earlyEngagement_lastShown")
        Logger.shared.log(.info, "EarlyEngagement", "📅 Сохранена дата показа сообщения: сегодня")
    }

    // MARK: - Streak System Methods

    /// Загружает историю работы для подсчета streak
    private func loadWorkHistory() {
        if let data = UserDefaults.standard.data(forKey: "earlyEngagement_workHistory"),
           let decoded = try? JSONDecoder().decode([WorkDay].self, from: data) {
            workHistory = decoded
            Logger.shared.log(.info, "EarlyEngagement", "📊 Загружена история работы: \(workHistory.count) записей")
        }

        // Очищаем старые записи (старше maxWorkHistoryDays дней)
        cleanupOldWorkHistory()

        // Пересчитываем текущий streak
        currentStreak = calculateCurrentStreak()
        Logger.shared.log(.info, "EarlyEngagement", "🔥 Текущий streak: \(currentStreak) дней")
    }

    /// Сохраняет историю работы
    private func saveWorkHistory() {
        if let encoded = try? JSONEncoder().encode(workHistory) {
            UserDefaults.standard.set(encoded, forKey: "earlyEngagement_workHistory")
        }
    }

    /// Очищает старые записи из истории работы
    private func cleanupOldWorkHistory() {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -maxWorkHistoryDays, to: Date()) ?? Date()
        workHistory = workHistory.filter { $0.date >= cutoffDate }
    }

    /// Вычисляет текущий streak (непрерывные дни работы)
    private func calculateCurrentStreak() -> Int {
        guard !workHistory.isEmpty else { return 0 }

        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        // Сортируем по дате (от новых к старым)
        let sortedHistory = workHistory.sorted { $0.date > $1.date }

        var streak = 0
        var currentDate = today

        // Идем назад по дням, считая непрерывные дни работы
        for workDay in sortedHistory {
            let workDate = calendar.startOfDay(for: workDay.date)

            // Если это текущий день или предыдущий
            if workDate == currentDate {
                if workDay.workedOnPriorityProject {
                    streak += 1
                    // Переходим к предыдущему дню
                    currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
                } else {
                    // Если в этот день не работали, streak прерывается
                    break
                }
            } else if workDate < currentDate {
                // Пропущен день - streak прерывается
                break
            }
            // Если workDate > currentDate, пропускаем (будущие записи)
        }

        return streak
    }

    /// Обновляет историю работы при завершении интервала
    private func updateWorkHistoryForToday(worked: Bool) {
        let today = Calendar.current.startOfDay(for: Date())

        // Ищем запись для сегодняшнего дня
        if let index = workHistory.firstIndex(where: { Calendar.current.isDate($0.date, inSameDayAs: today) }) {
            // Обновляем существующую запись
            workHistory[index] = WorkDay(date: today, workedOnPriorityProject: worked)
        } else {
            // Добавляем новую запись
            workHistory.append(WorkDay(date: today, workedOnPriorityProject: worked))
        }

        // Пересчитываем streak
        currentStreak = calculateCurrentStreak()

        // Сохраняем
        saveWorkHistory()

        Logger.shared.log(.info, "EarlyEngagement", "📊 Обновлена история работы: сегодня \(worked ? "работал" : "не работал"), streak: \(currentStreak)")
    }

    /// Определяет уровень вовлечения с учетом streak
    private func determineEngagementLevel() -> EngagementLevel {
        if daysWithoutWork == 0 {
            // Сегодня работал - используем streak
            return .workingToday(streakDays: currentStreak)
        } else {
            // Дни без работы - используем старую логику
            switch daysWithoutWork {
            case 1:
                return .oneDayOff
            case 2...3:
                return .twoDaysOff
            case 4...6:
                return .weekOff
            default:
                return .criticalOff
            }
        }
    }

    // MARK: - Debug Methods

    /// Преобразует количество дней без работы в индекс матрицы
    private func getDaysLevelIndex(daysWithoutWork: Int) -> Int {
        switch daysWithoutWork {
        case 0:
            return 0  // Уровень 0 (работал вчера)
        case 1:
            return 1  // Уровень 1 (1 день пропуск)
        case 2...3:
            return 2  // Уровень 2-3 (2-3 дня)
        case 4...6:
            return 3  // Уровень 4-6 (4-6 дней)
        default:
            return 4  // Уровень 7+ (неделя+)
        }
    }

    /// Преобразует индекс отладочного окна обратно в реальные дни
    /// Это обратная функция к getDaysLevelIndex для отладочного окна
    private func convertIndexToRealDays(index: Int) -> Int {
        switch index {
        case 0:
            return 0  // "0 дней (вчера работал)" → 0 дней
        case 1:
            return 1  // "1 день не работал" → 1 день
        case 2:
            // "2-3 дня не работал" → случайно выбираем 2 или 3 дня
            return Int.random(in: 2...3)
        case 3:
            // "4-6 дней не работал" → случайно выбираем 4, 5 или 6 дней
            return Int.random(in: 4...6)
        case 4:
            return 7  // "7+ дней не работал" → 7 дней (минимум для план-минимума)
        default:
            return 7  // По умолчанию максимальная эскалация
        }
    }

    /// Получает текущее сообщение для отладки
    func getCurrentMessage() -> EngagementMessage {
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ВХОД В getCurrentMessage()")

        let daysWithoutWork = calculateDaysWithoutWork()
        let timeOfDay = getCurrentTimeOfDay()
        let daysLevelIndex = getDaysLevelIndex(daysWithoutWork: daysWithoutWork)

        Logger.shared.log(.info, "EarlyEngagement", "🔧 Генерация сообщения для отладки: дни=\(daysWithoutWork), уровень=\(daysLevelIndex), время=\(timeOfDay)")

        // НОВАЯ СИСТЕМА: Определяем режим для отладки
        let message: EngagementMessage
        let streakDays = _debugStreakDays ?? currentStreak
        if streakDays > 0 {
            // Streak режим
            Logger.shared.log(.info, "EarlyEngagement", "🔥 Debug STREAK режим: \(streakDays) дней")
            message = NewMessageConstructionMatrix.getStreakMessage(streakDays: streakDays, messageIndex: timeOfDay)
        } else {
            // Return режим
            Logger.shared.log(.info, "EarlyEngagement", "🔄 Debug RETURN режим: \(daysWithoutWork) дней без работы")
            message = NewMessageConstructionMatrix.getReturnMessage(daysWithoutWork: daysWithoutWork, messageIndex: timeOfDay)
        }

        var processedMessage = substituteVariables(in: message)

        // ИСПРАВЛЕНИЕ: Используем ту же логику что и отладочное окно
        let initialBarMinutes = debugInitialBar ?? 52 // Используем отладочную планку или базовую 52 мин
        let calculatedBarMinutes = debugCalculateBarForScenario(
            initialBar: initialBarMinutes,
            daysWithoutWork: daysLevelIndex,
            messageIndex: timeOfDay
        )

        Logger.shared.log(.info, "EarlyEngagement", "🔧 ИСПРАВЛЕНО: Используем расчетную планку вместо currentUserBar")
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ОТЛАДКА ПЕРЕД ButtonMatrix: daysWithoutWork=\(daysWithoutWork), timeOfDay=\(timeOfDay)")
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ОТЛАДКА ПЕРЕД ButtonMatrix: initialBar=\(initialBarMinutes)мин, calculatedBar=\(calculatedBarMinutes)мин")
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ОТЛАДКА ПЕРЕД ButtonMatrix: старый buttonText='\(processedMessage.buttonText)'")

        let buttons = ButtonMatrix.generateButtonsForEarlyEngagement(
            daysWithoutWork: daysWithoutWork,
            messageIndex: timeOfDay,
            baseBarMinutes: initialBarMinutes
        )

        Logger.shared.log(.info, "EarlyEngagement", "🔧 ОТЛАДКА ButtonMatrix сгенерировал \(buttons.count) кнопок:")
        for (index, button) in buttons.enumerated() {
            Logger.shared.log(.info, "EarlyEngagement", "🔧   Кнопка \(index): '\(button.text)'")
        }

        // КРИТИЧЕСКИ ВАЖНО: Передаем кнопки в сообщение
        processedMessage.buttonMatrix = buttons

        // Обновляем текст основной кнопки (первая кнопка из ButtonMatrix)
        if let primaryButton = buttons.first {
            let oldText = processedMessage.buttonText
            processedMessage.buttonText = primaryButton.text
            Logger.shared.log(.info, "EarlyEngagement", "🔧 ОТЛАДКА ОБНОВЛЕН текст кнопки: '\(oldText)' → '\(primaryButton.text)'")
        } else {
            Logger.shared.log(.error, "EarlyEngagement", "🔧 ОТЛАДКА ОШИБКА: ButtonMatrix не вернул ни одной кнопки!")
        }

        return processedMessage
    }

    /// Принудительно показывает сообщение раннего вовлечения для тестирования
    func forceShowMessage() {
        Logger.shared.log(.info, "EarlyEngagement", "🔧 Принудительный показ сообщения для отладки")

        // Получаем текущее сообщение
        let message = getCurrentMessage()

        // Записываем статистику показа
        EngagementStatistics.shared.recordMessageShown(
            vertical: daysWithoutWork,
            horizontal: getCurrentTimeOfDay(),
            userBar: currentUserBar,
            daysWithoutWork: daysWithoutWork
        )

        // Показываем через делегат
        delegate?.showEngagementMessage(
            message,
            onAccept: { [weak self] projectId in
                Logger.shared.log(.info, "EarlyEngagement", "🔧 Отладка: Пользователь принял предложение")
                self?.handleUserAcceptance(projectId: projectId)
            },
            onDecline: { [weak self] in
                Logger.shared.log(.info, "EarlyEngagement", "🔧 Отладка: Пользователь отклонил предложение")
                self?.handleUserDecline()
            },
            onSnooze: { [weak self] in
                Logger.shared.log(.info, "EarlyEngagement", "🔧 Отладка: Пользователь отложил предложение")
                self?.handleUserSnooze(proposedDuration: 25 * 60) // 25 минут для отладки
            },
            onFullSession: { [weak self] projectId in
                Logger.shared.log(.info, "EarlyEngagement", "🔧 Отладка: Пользователь выбрал полную сессию")
                self?.handleFullSessionAcceptance(projectId: projectId, context: .earlyEngagement)
            }
        )
    }

    /// Получает текущие значения для отладки
    var debugCurrentUserBar: TimeInterval {
        return currentUserBar
    }



    var debugLastWorkTime: Date? {
        return lastWorkTime
    }

    var debugIsActive: Bool {
        return isActive
    }

    var debugCurrentTimeOfDay: Int {
        return getCurrentTimeOfDay()
    }

    // MARK: - Debug Methods

    /// Временная переменная для отладочной начальной планки
    private var debugInitialBar: Int? = nil

    /// Временная переменная для отладочных дней без работы
    private var _debugDaysWithoutWork: Int? = nil

    /// Временная переменная для отладочного времени дня (горизонтальный уровень)
    private var _debugHorizontalLevel: Int? = nil

    /// Getter для отладочных дней без работы (для совместимости с EarlyEngagementStatusWindow)
    var debugDaysWithoutWork: Int {
        return _debugDaysWithoutWork ?? daysWithoutWork
    }

    /// Устанавливает начальную планку для отладки (из отладочного окна)
    func debugSetInitialBar(_ barMinutes: Int) {
        debugInitialBar = barMinutes
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ОТЛАДКА: Установлена начальная планка \(barMinutes)мин")
    }

    /// Сбрасывает отладочную начальную планку
    func debugClearInitialBar() {
        debugInitialBar = nil
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ОТЛАДКА: Сброшена начальная планка")
    }

    /// Устанавливает дни без работы для отладки (из отладочного окна)
    func debugSetDaysWithoutWork(_ days: Int) {
        _debugDaysWithoutWork = days
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ОТЛАДКА: Установлены дни без работы \(days)")
    }

    /// Сбрасывает отладочные дни без работы
    func debugClearDaysWithoutWork() {
        _debugDaysWithoutWork = nil
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ОТЛАДКА: Сброшены дни без работы")
    }

    /// Устанавливает отладочный горизонтальный уровень (время дня)
    func debugSetHorizontalLevel(_ level: Int) {
        _debugHorizontalLevel = level
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ОТЛАДКА: Установлен горизонтальный уровень \(level)")
    }

    /// Сбрасывает отладочный горизонтальный уровень
    func debugClearHorizontalLevel() {
        _debugHorizontalLevel = nil
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ОТЛАДКА: Сброшен горизонтальный уровень")
    }

    func debugGetCurrentUserBar() -> TimeInterval {
        return currentUserBar
    }

    func debugGetDaysWithoutWork() -> Int {
        return daysWithoutWork
    }

    func debugGetLastWorkTime() -> Date? {
        return lastWorkTime
    }

    func debugGetIsActive() -> Bool {
        return isActive
    }

    /// Рассчитывает планку для отладочного сценария
    func debugCalculateBarForScenario(initialBar: Int, daysWithoutWork: Int, messageIndex: Int) -> Int {
        let initialBarTime = TimeInterval(initialBar * 60)

        // ВАЖНО: daysWithoutWork здесь - это индекс в массиве отладочного окна!
        // Преобразуем индекс обратно в реальные дни, чтобы использовать ту же логику что и в основном приложении
        let realDaysWithoutWork = convertIndexToRealDays(index: daysWithoutWork)

        // ВЕРТИКАЛЬНАЯ адаптация - используем ТУ ЖЕ ЛОГИКУ что в adaptUserBarByDaysWithoutWork()
        let verticalBarTime: TimeInterval
        switch realDaysWithoutWork {
        case 0:
            // Уровень 0 (работал вчера): планка растет по градационной системе
            verticalBarTime = GradualGrowthSystem.applyLevel0Growth(currentBar: initialBarTime)
        case 1:
            // Уровень 1 (1 день пропуск): планка × 0.77 (-23%) = ~40 мин
            verticalBarTime = initialBarTime * 0.77
        case 2...3:
            // Уровень 2-3 (2-3 дня): планка × 0.48 (-52%) = ~25 мин
            verticalBarTime = initialBarTime * 0.48
        case 4...6:
            // Уровень 4-6 (4-6 дней): планка × 0.29 (-71%) = ~15 мин
            verticalBarTime = initialBarTime * 0.29
        default:
            // Уровень 7+ (неделя+): планка = 3 минуты (план-минимум согласно документации)
            verticalBarTime = 3 * 60
        }

        // ГОРИЗОНТАЛЬНАЯ дескалация (по времени дня)
        let finalBarTime: TimeInterval
        switch messageIndex {
        case 0:
            // 1-е предложение: 100% планки (но не меньше план-минимума)
            finalBarTime = max(verticalBarTime, 3 * 60)
        case 1:
            // 2-е предложение: 50% планки (но не меньше план-минимума)
            finalBarTime = max(verticalBarTime * 0.5, 3 * 60)
        case 2:
            // 3-е предложение: 25% планки (но не меньше план-минимума)
            finalBarTime = max(verticalBarTime * 0.25, 3 * 60)
        case 3:
            // 4-е предложение: план-минимум (3 мин)
            finalBarTime = 3 * 60
        default:
            finalBarTime = max(verticalBarTime, 3 * 60)
        }

        // Ограничиваем максимум 52 минуты
        let limitedBarTime = min(finalBarTime, 52 * 60)

        return Int(limitedBarTime / 60)
    }

    /// Рассчитывает планку для streak-режима в отладочном окне
    func debugCalculateBarForStreak(initialBar: Int, streakDays: Int, messageIndex: Int) -> Int {
        let initialBarTime = TimeInterval(initialBar * 60)

        // STREAK ЛОГИКА: Применяем градационный рост несколько раз
        // Каждый день streak планка росла по GradualGrowthSystem
        var currentBarTime = initialBarTime

        // Применяем рост streak раз (симулируем накопленный рост)
        for _ in 0..<streakDays {
            currentBarTime = GradualGrowthSystem.applyLevel0Growth(currentBar: currentBarTime)
            // Ограничиваем максимум 52 минуты на каждом шаге
            currentBarTime = min(currentBarTime, 52 * 60)
        }

        Logger.shared.log(.info, "EarlyEngagement", "🔥 Streak \(streakDays) дней: \(initialBar)мин → \(Int(currentBarTime/60))мин")

        // ГОРИЗОНТАЛЬНАЯ дескалация (по времени дня)
        let finalBarTime: TimeInterval
        switch messageIndex {
        case 0:
            // 1-е предложение: 100% планки (но не меньше план-минимума)
            finalBarTime = max(currentBarTime, 3 * 60)
        case 1:
            // 2-е предложение: 50% планки (но не меньше план-минимума)
            finalBarTime = max(currentBarTime * 0.5, 3 * 60)
        case 2:
            // 3-е предложение: 25% планки (но не меньше план-минимума)
            finalBarTime = max(currentBarTime * 0.25, 3 * 60)
        case 3:
            // 4-е предложение: план-минимум (3 мин)
            finalBarTime = 3 * 60
        default:
            finalBarTime = max(currentBarTime, 3 * 60)
        }

        // Ограничиваем максимум 52 минуты
        let limitedBarTime = min(finalBarTime, 52 * 60)

        return Int(limitedBarTime / 60)
    }

    /// Рассчитывает планку для реального окна (без преобразования индексов)
    private func calculateBarForRealWindow(initialBar: Int, daysWithoutWork: Int, messageIndex: Int) -> Int {
        let initialBarTime = TimeInterval(initialBar * 60)

        Logger.shared.log(.info, "EarlyEngagement", "🔍 calculateBarForRealWindow ВХОД: initialBar=\(initialBar)мин, daysWithoutWork=\(daysWithoutWork), messageIndex=\(messageIndex)")

        // ИСПОЛЬЗУЕМ РЕАЛЬНЫЕ дни без работы напрямую (без преобразования через индексы)
        // Это обеспечивает точное соответствие с ButtonMatrix

        // ВЕРТИКАЛЬНАЯ адаптация - используем ТУ ЖЕ ЛОГИКУ что в adaptUserBarByDaysWithoutWork()
        let verticalBarTime: TimeInterval
        switch daysWithoutWork {
        case 0:
            // Уровень 0 (работал вчера): проверяем streak режим
            if let debugStreak = _debugStreakDays {
                // STREAK РЕЖИМ: Применяем градационный рост несколько раз
                var currentBarTime = initialBarTime
                for _ in 0..<debugStreak {
                    currentBarTime = GradualGrowthSystem.applyLevel0Growth(currentBar: currentBarTime)
                    currentBarTime = min(currentBarTime, 52 * 60) // Ограничиваем на каждом шаге
                }
                verticalBarTime = currentBarTime
                Logger.shared.log(.info, "EarlyEngagement", "🔍 case 0 STREAK \(debugStreak): \(initialBar)мин → \(Int(verticalBarTime/60))мин (GradualGrowth×\(debugStreak))")
            } else {
                // Обычный режим: планка растет по градационной системе один раз
                verticalBarTime = GradualGrowthSystem.applyLevel0Growth(currentBar: initialBarTime)
                Logger.shared.log(.info, "EarlyEngagement", "🔍 case 0: \(initialBar)мин → \(Int(verticalBarTime/60))мин (GradualGrowth)")
            }
        case 1:
            // Уровень 1 (1 день пропуск): планка × 0.77 (-23%) = ~40 мин
            verticalBarTime = initialBarTime * 0.77
        case 2...3:
            // Уровень 2-3 (2-3 дня): планка × 0.48 (-52%) = ~25 мин
            verticalBarTime = initialBarTime * 0.48
        case 4...6:
            // Уровень 4-6 (4-6 дней): планка × 0.29 (-71%) = ~15 мин
            verticalBarTime = initialBarTime * 0.29
        default:
            // Уровень 7+ (неделя+): планка = 3 минуты (план-минимум согласно документации)
            verticalBarTime = 3 * 60
        }

        // ГОРИЗОНТАЛЬНАЯ дескалация (по времени дня) - ТА ЖЕ ЛОГИКА что в ButtonMatrix
        let finalBarTime: TimeInterval
        switch messageIndex {
        case 0:
            // 1-е предложение: 100% планки (но не меньше план-минимума)
            finalBarTime = max(verticalBarTime, 3 * 60)
        case 1:
            // 2-е предложение: 50% планки (но не меньше план-минимума)
            finalBarTime = max(verticalBarTime * 0.5, 3 * 60)
        case 2:
            // 3-е предложение: 25% планки (но не меньше план-минимума)
            finalBarTime = max(verticalBarTime * 0.25, 3 * 60)
        case 3:
            // 4-е предложение: план-минимум (3 мин)
            finalBarTime = 3 * 60
        default:
            finalBarTime = max(verticalBarTime, 3 * 60)
        }

        // Ограничиваем максимум 52 минуты
        let limitedBarTime = min(finalBarTime, 52 * 60)

        let result = Int(limitedBarTime / 60)
        Logger.shared.log(.info, "EarlyEngagement", "🔍 calculateBarForRealWindow РЕЗУЛЬТАТ: \(initialBar)мин → \(result)мин")

        return result
    }

    /// Генерирует текст формулы для отладки
    func debugGetFormulaText(daysLevel: Int, messageIndex: Int, initialBar: Int, calculatedBar: Int) -> String {
        // ВАЖНО: daysLevel здесь - это индекс в массиве отладочного окна!
        // Преобразуем индекс в реальные дни для согласованности с основной логикой
        let realDaysWithoutWork = convertIndexToRealDays(index: daysLevel)

        // Сначала показываем вертикальную адаптацию - используем ТУ ЖЕ ЛОГИКУ что в adaptUserBarByDaysWithoutWork()
        let verticalText: String
        switch realDaysWithoutWork {
        case 0:
            let growthDesc = GradualGrowthSystem.getGrowthDescription(currentBarMinutes: initialBar)
            verticalText = "\(initialBar) \(growthDesc)"
        case 1:
            verticalText = "\(initialBar) × 0.77"
        case 2...3:
            verticalText = "\(initialBar) × 0.48"
        case 4...6:
            verticalText = "\(initialBar) × 0.29"
        default:
            verticalText = "план-минимум (3 мин)"
        }

        // Затем показываем горизонтальную дескалацию
        let horizontalText: String
        switch messageIndex {
        case 0:
            horizontalText = "× 1.0 (100%)"
        case 1:
            horizontalText = "× 0.5 (50%)"
        case 2:
            horizontalText = "× 0.25 (25%)"
        case 3:
            horizontalText = "→ 3 мин (план-мин.)"
        default:
            horizontalText = ""
        }

        if daysLevel >= 7 && messageIndex >= 2 {
            return "план-минимум = \(calculatedBar) мин"
        } else if messageIndex >= 2 {
            return "\(verticalText) \(horizontalText) = \(calculatedBar) мин"
        } else {
            return "\(verticalText) \(horizontalText) = \(calculatedBar) мин"
        }
    }

    func debugGetCurrentMessage() -> EngagementMessage? {
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ВХОД В debugGetCurrentMessage() - НОВАЯ СИСТЕМА!")

        let timeOfDay = getCurrentTimeOfDay()
        let streakDays = _debugStreakDays ?? currentStreak

        if streakDays > 0 {
            // Streak режим
            Logger.shared.log(.info, "EarlyEngagement", "🔥 Debug STREAK режим: \(streakDays) дней")
            return NewMessageConstructionMatrix.getStreakMessage(streakDays: streakDays, messageIndex: timeOfDay)
        } else {
            // Return режим
            let daysWithoutWork = calculateDaysWithoutWork()
            Logger.shared.log(.info, "EarlyEngagement", "🔄 Debug RETURN режим: \(daysWithoutWork) дней без работы")
            return NewMessageConstructionMatrix.getReturnMessage(daysWithoutWork: daysWithoutWork, messageIndex: timeOfDay)
        }
    }

    func debugCreateDemoMessage() -> EngagementMessage {
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ВХОД В debugCreateDemoMessage() - НОВАЯ СИСТЕМА!")

        let streakDays = _debugStreakDays ?? currentStreak
        let horizontal = 0 // morning

        if streakDays > 0 {
            // Streak режим
            Logger.shared.log(.info, "EarlyEngagement", "🔥 Demo STREAK режим: \(streakDays) дней")
            return NewMessageConstructionMatrix.getStreakMessage(streakDays: streakDays, messageIndex: horizontal)
        } else {
            // Return режим
            let daysWithoutWork = max(1, calculateDaysWithoutWork())
            Logger.shared.log(.info, "EarlyEngagement", "🔄 Demo RETURN режим: \(daysWithoutWork) дней без работы")
            return NewMessageConstructionMatrix.getReturnMessage(daysWithoutWork: daysWithoutWork, messageIndex: horizontal)
        }
    }

    func debugSimulateWakeEvent() {
        Logger.shared.log(.info, "EarlyEngagement", "🔧 Debug: Симуляция события пробуждения")
        // Симулируем пробуждение после 8 часов сна
        let sleepDuration: TimeInterval = 8 * 60 * 60 // 8 часов
        let wakeTime = Date()
        handleWakeUpEvent(sleepDuration: sleepDuration, wakeTime: wakeTime)
    }

    func debugRecalculateDaysWithoutWork() {
        Logger.shared.log(.info, "EarlyEngagement", "🔧 Debug: Принудительный пересчет дней без работы")
        _ = calculateDaysWithoutWork()
        adaptUserBarByDaysWithoutWork()
        saveUserBarData()
    }

    func debugResetLastWorkTime() {
        Logger.shared.log(.info, "EarlyEngagement", "🔧 Debug: Сброс времени последней работы")
        lastWorkTime = nil
        UserDefaults.standard.removeObject(forKey: "earlyEngagement_lastWork")
        _ = calculateDaysWithoutWork()
        adaptUserBarByDaysWithoutWork()
        saveUserBarData()
        Logger.shared.log(.info, "EarlyEngagement", "✅ Время последней работы сброшено")
    }

    // MARK: - Debug Methods for Streak System

    /// Отладочная переменная для streak
    private var _debugStreakDays: Int? = nil

    /// Устанавливает streak для отладки
    func debugSetStreakDays(_ days: Int) {
        _debugStreakDays = days
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ОТЛАДКА: Установлен streak \(days) дней")
    }

    /// Сбрасывает отладочный streak
    func debugClearStreakDays() {
        _debugStreakDays = nil
        Logger.shared.log(.info, "EarlyEngagement", "🔧 ОТЛАДКА: Сброшен streak")
    }

    /// Получает текущий streak для отладки
    func debugGetCurrentStreak() -> Int {
        return _debugStreakDays ?? currentStreak
    }

    /// Получает историю работы для отладки
    func debugGetWorkHistory() -> [WorkDay] {
        return workHistory
    }

    /// Получает текущий уровень вовлечения для отладки
    func debugGetEngagementLevel() -> EngagementLevel {
        // Если установлен отладочный streak, используем его
        if let debugStreak = _debugStreakDays {
            return .workingToday(streakDays: debugStreak)
        }

        // Если установлены отладочные дни без работы, используем их
        if let debugDays = _debugDaysWithoutWork {
            if debugDays == 0 {
                return .workingToday(streakDays: currentStreak)
            } else {
                switch debugDays {
                case 1:
                    return .oneDayOff
                case 2...3:
                    return .twoDaysOff
                case 4...6:
                    return .weekOff
                default:
                    return .criticalOff
                }
            }
        }

        // Используем реальную логику
        return determineEngagementLevel()
    }

    /// Сбрасывает всю историю работы (для отладки)
    func debugResetWorkHistory() {
        Logger.shared.log(.info, "EarlyEngagement", "🔧 Debug: Сброс истории работы")
        workHistory.removeAll()
        currentStreak = 0
        UserDefaults.standard.removeObject(forKey: "earlyEngagement_workHistory")
        Logger.shared.log(.info, "EarlyEngagement", "✅ История работы сброшена")
    }

    // MARK: - Public Methods for UI Display

    /// Получает текущий streak для отображения в UI
    func getCurrentStreakForDisplay() -> Int {
        Logger.shared.log(.info, "EarlyEngagement", "🔍 ОТЛАДКА getCurrentStreakForDisplay: currentStreak=\(currentStreak), workHistory.count=\(workHistory.count)")

        // Показываем последние записи истории работы
        let recentHistory = workHistory.suffix(3)
        for workDay in recentHistory {
            let formatter = DateFormatter()
            formatter.dateFormat = "dd.MM"
            Logger.shared.log(.info, "EarlyEngagement", "📊 История: \(formatter.string(from: workDay.date)) - \(workDay.workedOnPriorityProject ? "работал" : "не работал")")
        }

        return currentStreak
    }

    /// Получает максимальный streak за всю историю для отображения в UI
    func getMaxStreakForDisplay() -> Int {
        // Находим максимальную последовательность дней работы в истории
        var maxStreak = 0
        var currentSequence = 0

        // Сортируем по дате и проходим по всей истории
        let sortedHistory = workHistory.sorted { $0.date < $1.date }

        for workDay in sortedHistory {
            if workDay.workedOnPriorityProject {
                currentSequence += 1
                maxStreak = max(maxStreak, currentSequence)
            } else {
                currentSequence = 0
            }
        }

        return max(maxStreak, currentStreak) // Учитываем и текущий streak
    }

    // MARK: - Debug Methods for Status Display

    /// Получает время последнего показа сообщения
    func debugGetLastMessageShownTime() -> Date? {
        return UserDefaults.standard.object(forKey: "earlyEngagement_lastShown") as? Date
    }

    /// Получает время пробуждения сегодня
    func debugGetTodayWakeUpTime() -> Date? {
        return todayWakeUpTime
    }

    /// Получает информацию о состоянии таймеров повторных показов
    func debugGetRepeatTimersInfo() -> (activeTimers: Int, shownMessages: Set<Int>) {
        return (activeTimers: repeatTimers.count, shownMessages: shownMessageIndexes)
    }

    /// Получает информацию о следующем ожидаемом показе
    func debugGetNextShowInfo() -> String {
        // Если нет времени пробуждения, значит система не активна
        guard todayWakeUpTime != nil else {
            return "Система ожидает пробуждения"
        }

        let now = Date()

        // Проверяем какие сообщения уже показали
        let shownCount = shownMessageIndexes.count

        if shownCount == 0 {
            // Еще не показывали первое сообщение
            if waitingForActivity {
                return "Ожидание активности пользователя"
            } else {
                return "Первое сообщение должно было показаться при активности"
            }
        }

        // ИСПРАВЛЕНИЕ: Используем время первого показа для расчета
        guard let firstShownTime = firstMessageShownTime else {
            return "Ошибка: нет времени первого показа"
        }

        let timeSinceFirstShow = now.timeIntervalSince(firstShownTime)

        // Определяем следующий показ (интервалы от первого показа)
        let intervals: [(minutes: Int, messageIndex: Int)] = [
            (20, 1),   // 20 минут - второе сообщение
            (60, 2),   // 1 час - третье сообщение
            (120, 3)   // 2 часа - четвертое сообщение
        ]

        for interval in intervals {
            let targetTime = interval.minutes * 60
            if !shownMessageIndexes.contains(interval.messageIndex) {
                if timeSinceFirstShow < TimeInterval(targetTime) {
                    let remainingMinutes = (targetTime - Int(timeSinceFirstShow)) / 60
                    return "Следующий показ через \(remainingMinutes) мин (сообщение \(interval.messageIndex + 1))"
                } else {
                    return "Сообщение \(interval.messageIndex + 1) должно было показаться \(Int(timeSinceFirstShow - TimeInterval(targetTime))/60) мин назад"
                }
            }
        }

        return "Все сообщения показаны"
    }

    /// Получает статус системы повторных показов
    func debugGetRepeatSystemStatus() -> String {
        guard let wakeUpTime = todayWakeUpTime else {
            return "❌ Система не активна (нет времени пробуждения)"
        }

        let now = Date()
        let timeSinceWakeUp = now.timeIntervalSince(wakeUpTime)
        let minutesSinceWakeUp = Int(timeSinceWakeUp / 60)

        let activeTimers = repeatTimers.count
        let shownCount = shownMessageIndexes.count

        var status = "✅ Система активна\n"
        status += "⏰ Прошло с пробуждения: \(minutesSinceWakeUp) мин\n"

        // НОВОЕ: Показываем время с первого показа, если он был
        if let firstShownTime = firstMessageShownTime {
            let timeSinceFirstShow = now.timeIntervalSince(firstShownTime)
            let minutesSinceFirstShow = Int(timeSinceFirstShow / 60)
            status += "🎯 Прошло с первого показа: \(minutesSinceFirstShow) мин\n"
        }

        status += "📊 Показано сообщений: \(shownCount)/4\n"
        status += "⏲️ Активных таймеров: \(activeTimers)\n"

        if waitingForActivity {
            status += "⏳ Ожидание активности пользователя"
        } else if shownCount > 0 {
            status += "🎯 Система повторных показов запущена"
        }

        return status
    }
}

// MARK: - Supporting Data Structures

/// Сообщение раннего вовлечения
struct EngagementMessage: Codable {
    var title: String
    var subtitle: String
    var proposedDuration: TimeInterval
    var buttonText: String
    var level: Int // Уровень эскалации (0-4)
    var timeOfDay: Int // Время дня (0-3)

    // Поддержка системы полной сессии
    var showFullSessionButton: Bool = false
    var fullSessionButtonText: String = "Полная сессия (52 мин)"
    var sessionContext: FullSessionContext = .earlyEngagement

    // НОВАЯ СИСТЕМА: Кнопки от ButtonMatrix (не сохраняется в Codable)
    var buttonMatrix: [ButtonMatrix.ButtonComponent]? = nil

    // Исключаем buttonMatrix из кодирования
    enum CodingKeys: String, CodingKey {
        case title, subtitle, proposedDuration, buttonText, level, timeOfDay
        case showFullSessionButton, fullSessionButtonText, sessionContext
    }
}

/// Контекст для системы полной сессии
enum FullSessionContext: String, Codable {
    case earlyEngagement = "early_engagement"
    case horizontalDescaling = "horizontal_descaling"
    case verticalDegradation = "vertical_degradation"
    case restCompletion = "rest_completion"
}

/// Запись изменения планки пользователя
struct UserBarEntry: Codable {
    let timestamp: Date
    let oldValue: TimeInterval
    let newValue: TimeInterval
    let reason: UserBarChangeReason
    let intervalDuration: TimeInterval
}

/// Причина изменения планки
enum UserBarChangeReason: String, Codable {
    case success = "success"
    case failure = "failure"
    case adaptation = "adaptation"
    case reset = "reset"
    case sessionGrowth = "sessionGrowth"
}

// MARK: - Protocols

/// Протокол делегата системы раннего вовлечения
protocol EarlyEngagementSystemDelegate: AnyObject {
    /// Показать сообщение раннего вовлечения с колбэками
    func showEngagementMessage(
        _ message: EngagementMessage,
        onAccept: @escaping (UUID?) -> Void,
        onDecline: @escaping () -> Void,
        onSnooze: @escaping () -> Void,
        onFullSession: @escaping (UUID?) -> Void
    )

    /// Запустить интервал с указанной длительностью
    func startInterval(duration: TimeInterval, projectId: UUID?)

    /// Записать статистику события
    func recordEngagementStatistics(_ event: EngagementEvent)
}
