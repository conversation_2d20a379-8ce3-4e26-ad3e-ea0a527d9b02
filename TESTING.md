# 🧪 Документация по тестированию uProd

## 📋 Обзор системы тестирования

uProd использует **автоматическую модульную систему тестирования** с реальной логикой для обеспечения максимального качества и надежности приложения.

### 🆕 **ПОСЛЕДНЕЕ ОБНОВЛЕНИЕ: Исправлен баг с неучетом времени сна в неформальных сессиях (05.08.2025)**

**Проблема (РЕШЕНА):** Система не записывала неактивные минуты за время сна компьютера, что приводило к неправильному отображению статуса неформальных сессий.

**Симптомы:**
- Пользователь работает 2 минуты → закрывает ноутбук на 5 минут → открывает
- Ожидается: 2 зеленых + 5 черных кружочков = 7 кружочков
- Реально было: только 2 зеленых кружочка (время сна игнорировалось)

**Исправление:**
- Добавлен метод `recordSleepInactivity()` в `InformalSessionDetector`, `ActivityStateManager` и `MinuteActivityTracker`
- При пробуждении система теперь записывает неактивные минуты за время сна во все системы отслеживания
- Обновлен `AppDelegate` для вызова новых методов при событиях сна/пробуждения и длительной неактивности
- Исправлена интеграция между `MinuteActivityTracker` (источник данных для статуса) и системой сна/пробуждения

**Тестирование:**
- Добавлен тест `🚨 БАГ: Время сна НЕ учитывается в неформальных сессиях`
- Добавлен тест `🧪 РЕАЛЬНЫЙ СЦЕНАРИЙ: Работа -> сон -> работа -> проверка окна`
- Добавлен тест `IntervalResetTest.swift` для проверки логики сброса интервала
- Добавлен тест `SleepResetLogicTest.swift` для проверки порогов времени
- Все тесты проходят успешно (13/13 + 4/4 новых)
- Приложение успешно собрано с исправлениями (версия 0.2.8.3)

## 🔄 Дополнительное исправление: Сброс интервала при длительной неактивности

**Проблема:** Интервал не сбрасывался при длительной неактивности (15+ минут)

**Исправление:**
- Реализована логика сброса интервала в `ActivityStateManager.swift`
- Добавлен callback `onIntervalReset` для уведомления о сбросе
- Подключен обработчик в `AppDelegate.swift`

**Логика сброса:**
- **0-10 минут неактивности:** Интервал продолжается
- **10+ минут неактивности:** Интервал приостанавливается (⏸️)
- **15+ минут неактивности:** Интервал сбрасывается (🔄)
- **30+ минут сна:** Лог активности полностью сбрасывается

**Тестирование:**
- Создан `IntervalResetTest.swift` с мок-объектами для проверки логики
- Создан `SleepResetLogicTest.swift` для проверки порогов времени
- Все тесты логики проходят успешно (4/4)

---

### **ПРЕДЫДУЩЕЕ ОБНОВЛЕНИЕ: Исправление UI вкладки "Временные" в настройках**

**Проблема (РЕШЕНА):** Выпадающий список позиционирования окон во вкладке "Временные" имел некорректное отображение
- **Симптом:** Огромные отступы справа, список не кликался, выглядел "стремно" по сравнению с другими вкладками
- **Причина:** Отсутствовала стилизация и фиксированная ширина для PopUpButton в setupTemporaryTab()
- **Решение:** Добавлена стилизация (wantsLayer, cornerRadius, backgroundColor) и фиксированная ширина (140px) как у других PopUpButton
- **Результат:** Выпадающий список теперь выглядит и работает как в других вкладках
- **Тест:** `Tests/TemporarySettingsUITest.swift` - проверяет наличие всех исправлений в коде

**Предыдущие исправления:**

**Проблема 1 (РЕШЕНА):** Debug окно показывало неправильные значения для сценария "7+ дней не работал"
- **Симптом:** "рассчитанная планка: 3 мин" но кнопка показывала "11 мин"
- **Причина:** Неправильный маппинг индекса выпадающего списка к бизнес-логике
- **Решение:** Добавлена функция `mapIndexToDays()` в `EarlyEngagementDebugWindow.swift`

**Проблема 2 (РЕШЕНА):** Debug окно показывало неправильные значения для сценария "4-6 дней не работал"
- **Симптом:** "рассчитанная планка: 3 мин" но кнопка показывала "10-14 мин"
- **Причина:** Двойное маппинг - debug окно мапило индекс в дни, а потом EarlyEngagementSystem мапил дни обратно в индекс
- **Решение:** Убрали двойное маппинг - передаем индекс напрямую в `debugCalculateBarForScenario()`
- **Результат:** Debug окно и кнопки теперь показывают одинаковые значения
- **Тест:** `Tests/DebugWindowMappingTest.swift` - проверяет правильность маппинга и отсутствие двойного преобразования

## 🎯 Принципы тестирования

### **⚠️ КРИТИЧЕСКИ ВАЖНО: Только реальная логика!**

**✅ ПРАВИЛЬНО:**
- Тестировать **РЕАЛЬНЫЕ** классы и методы приложения
- Использовать **настоящую логику** InformalSessionDetector, PomodoroTimer, etc.
- Изолировать только **внешние зависимости** (UI, файлы, сеть, таймеры)
- Тесты должны быть **максимально близки к реальности**

**❌ НЕПРАВИЛЬНО:**
- Создавать упрощенные версии основной логики
- Использовать "TestableXXX" классы вместо реальных
- Мокать основную бизнес-логику
- Создавать "игрушечные" тесты ради тестов

### **🔍 Философия: "Тесты как страховка"**
- Тесты должны **реально защищать** от регрессий
- Если тест проходит, но реальный код сломан - тест бесполезен
- Лучше **меньше тестов, но качественных**, чем много поверхностных
- Каждый тест должен **находить реальные проблемы**

## 🎯 **КРИТИЧЕСКИ ВАЖНО: Типы тестов и защита от регрессий**

### **🚨 ГЛАВНАЯ ОПАСНОСТЬ: Mock-тесты = ИЛЛЮЗИЯ БЕЗОПАСНОСТИ!**

**Реальная проблема Mock-тестов:**
- ✅ **Mock-тесты проходят** - показывают "все хорошо"
- ❌ **Реальный код сломан** - приложение не работает
- 🐛 **Проблема:** Mock-тесты тестируют СВОЮ логику, а не реальную
- 💥 **Результат:** Ложная уверенность в качестве кода

### **⚠️ ОПАСНОСТЬ: Только Mock-тесты = НЕТ ЗАЩИТЫ ОТ РЕГРЕССИЙ!**

**Реальный эксперимент из проекта:**
1. **Сломали реальную логику** в AppDelegate (окно не показывается)
2. **Mock-тесты прошли** ✅ (5/5) - показали "все хорошо"
3. **Реальные тесты упали** ❌ - поймали проблему
4. **Вывод:** Mock-тесты НЕ защищают от регрессий!

### **🔬 ТИП 1: MOCK-ТЕСТЫ (НЕ РЕКОМЕНДУЕТСЯ для защиты от регрессий)**
- **Что тестируют:** Свою собственную Mock-логику
- **Пример:** MockInformalSessionDetector с MockWindowManager
- **Цель:** Проверить алгоритмы в изоляции
- **Скорость:** Очень быстрые (<0.001с)
- **⚠️ ПРОБЛЕМА:** НЕ защищают от поломок реального кода
- **Когда использовать:** Только для тестирования чистых алгоритмов

### **✅ ТИП 2: РЕАЛЬНЫЕ ТЕСТЫ (РЕКОМЕНДУЕТСЯ для защиты от регрессий)**
- **Что тестируют:** Реальный код приложения
- **Пример:** Читают AppDelegate.swift и проверяют логику
- **Цель:** Поймать реальные поломки в коде
- **Скорость:** Быстрые (<1с)
- **✅ ПРЕИМУЩЕСТВО:** РЕАЛЬНО защищают от регрессий
- **Когда использовать:** ВСЕГДА для критической логики

### **🔗 ТИП 3: ИНТЕГРАЦИОННЫЕ ТЕСТЫ (ОБЯЗАТЕЛЬНО)**
- **Что тестируют:** Полный путь от входа до результата
- **Пример:** От `recordMinuteActivity()` до показа окна пользователю
- **Цель:** Проверить что компоненты работают ВМЕСТЕ
- **Скорость:** Быстрые (<1с)
- **Когда обязательны:** Для критической логики с несколькими компонентами

## 🎯 **ПРИНЦИП: ОДИН ТЕСТ НА ФУНКЦИЮ**

### **📋 ПРАВИЛО: Для каждой функции создавай ТОЛЬКО ОДИН главный тест!**

**✅ ПРАВИЛЬНО:**
- **InformalSessionTest.swift** - тест неформальных сессий
- **PomodoroTimerTest.swift** - тест помодоро таймера
- **RealAppDelegateLogicTest.swift** - тест логики показа окна
- **BreakSystemTest.swift** - тест системы отдыха

**❌ НЕПРАВИЛЬНО:**
- 10+ тестов для одной функции показа окна
- WindowTest1, WindowTest2, WindowTest3...
- Дублирование тестов с разными названиями

### **🎯 ПРИОРИТЕТЫ ТЕСТИРОВАНИЯ (в порядке важности)**

#### **1️⃣ ВЫСШИЙ ПРИОРИТЕТ: Реальные тесты**
- **Цель:** Защита от регрессий в реальном коде
- **Что делать:** Читать исходный код и проверять логику
- **Пример:** `Tests/RealAppDelegateLogicTest.swift`
- **⚠️ БЕЗ ЭТОГО:** Нет защиты от поломок!

#### **2️⃣ ВЫСОКИЙ ПРИОРИТЕТ: Интеграционные тесты**
- **Цель:** Проверить что компоненты работают вместе
- **Что делать:** Тестировать полный путь от входа до результата
- **Пример:** От записи активности до показа окна
- **⚠️ БЕЗ ЭТОГО:** Компоненты могут не работать вместе!

#### **3️⃣ СРЕДНИЙ ПРИОРИТЕТ: Mock-тесты алгоритмов**
- **Цель:** Проверить чистые алгоритмы и математику
- **Что делать:** Тестировать логику в изоляции
- **Пример:** Подсчет активных минут
- **⚠️ ВНИМАНИЕ:** НЕ защищают от регрессий в реальном коде!

### **📋 ПРАВИЛО: Начинай с реального теста, НЕ создавай дубликаты!**

## 🧪 **Практические рекомендации**

### **🚀 Как создавать качественные тесты (правильный порядок):**

**1. СНАЧАЛА: Реальный тест (защита от регрессий):**
```swift
// ✅ ПРАВИЛЬНО: Проверяем реальный код
func testRealAppDelegateLogic() {
    let content = try! String(contentsOfFile: "AppDelegate.swift")

    // Проверяем что нет сломанной логики
    let hasBrokenLogic = content.contains("🐛 ВРЕМЕННО СЛОМАННАЯ ЛОГИКА")
    assert(!hasBrokenLogic, "Обнаружена сломанная логика!")

    // Проверяем что есть правильная логика
    let hasCorrectLogic = content.contains("existingWindow.isVisible")
    assert(hasCorrectLogic, "Правильная логика не найдена!")
}
```

**2. ПОТОМ: Интеграционный тест (полный путь):**
```swift
// ✅ ПРАВИЛЬНО: Тестируем полный путь
func testFullWorkflow() {
    // Полный путь от входа до результата
    detector.recordActivity(true)    // Вход
    detector.checkConditions()       // Обработка
    let result = app.showWindow()    // Результат
    assert(result == true)           // Проверка
}
```

**3. В КОНЦЕ: Mock-тесты (только для алгоритмов):**
```swift
// ⚠️ ОСТОРОЖНО: НЕ защищает от регрессий!
func testMockCalculation() {
    let mockDetector = MockDetector()
    let result = mockDetector.calculate([true, false])
    assert(result == 1)  // Тестирует только Mock-логику
}
```

**3. Тестируй реальные сценарии:**
- ✅ "Пользователь работал 55 минут - должно показаться окно"
- ❌ "Функция возвращает true при входе X"

**4. Проверяй граничные случаи:**
- Ровно на границе срабатывания (42 минуты)
- Чуть меньше границы (41 минута)
- Недостаток данных (30 минут)

### **⚠️ Типичные ошибки и как их избежать:**

**❌ ОШИБКА 1: "Тестирую только логику"**
```swift
// Плохо - тестирует только математику
func testShouldTrigger() {
    assert(detector.shouldTrigger() == true)
}
```
**✅ РЕШЕНИЕ: Добавь интеграционный тест**
```swift
// Хорошо - тестирует полный сценарий
func testUserWorked55Minutes() {
    // Симулируем реальную работу пользователя
    for _ in 1...55 { detector.recordActivity(true) }
    // Проверяем что система РЕАЛЬНО сработает
    assert(detector.wouldShowWindow() == true)
}
```

**❌ ОШИБКА 2: "Тесты проходят, но приложение не работает"**
- **Причина:** Тестируешь изолированные компоненты, но не их взаимодействие
- **Решение:** Создай тест полного пути от входа до результата

**❌ ОШИБКА 3: "Мокаю всё подряд"**
- **Причина:** Боишься медленных тестов
- **Решение:** Мокай только внешние зависимости (UI, файлы, сеть)

## 🏗️ Архитектура тестов

### **Уровень 1: Автоматические юнит-тесты (командная строка)**
- **Запускаются при каждой сборке** через `./build.sh`
- **Тестируют реальную логику** приложения без упрощений
- **Быстрые** (<1 секунды) и **детерминированные**
- **Останавливают сборку** при обнаружении проблем
- **Модульная структура** - каждый компонент отдельно

### **Уровень 2: Интеграционные тесты (через интерфейс)**
- Доступны через меню "🧪 Другие тесты → 🤖 Автотест неформальных сессий"
- **Тестируют взаимодействие** компонентов в реальном приложении
- **Используют реальные классы** с минимальными моками
- Для **финальной проверки** перед релизом

### **Уровень 3: Ручные тесты (через интерфейс)**
- Для **отладки и демонстрации** конкретных сценариев
- **Визуальная проверка** UI компонентов
- Доступны через меню "🧪 Другие тесты"

## 📁 Структура тестов

```
Tests/
├── InformalSessionTest.swift       ✅ 6 тестов  - Неформальные сессии
├── PomodoroTimerTest.swift         ✅ 5 тестов  - Формальные интервалы
├── BreakSystemTest.swift           ✅ 6 тестов  - Система отдыха
├── WindowDisplayTest.swift         ✅ 4 теста   - Показ окна (с моками)
├── IntegratedWindowTest.swift      ✅ 4 теста   - Интегрированные тесты окна
├── WindowLogicTest.swift           ✅ 8 тестов  - Логика показа окна [КРИТИЧЕСКИЙ]
├── DebugWindowMappingTest.swift    ✅ 1 тест    - Маппинг индексов debug окна
├── TemporarySettingsUITest.swift   ✅ 4 теста   - UI вкладки "Временные" [НОВЫЙ]
├── ActivityDetectionTest.swift     📋 Планируется - Детекция активности
├── SleepWakeTest.swift            📋 Планируется - Сон/пробуждение
├── ProjectManagementTest.swift    📋 Планируется - Управление проектами
├── StatisticsTest.swift           📋 Планируется - Статистика
└── IntegrationTest.swift          📋 Планируется - Интеграционные тесты
```

**📊 Быстрая навигация:**
- [📋 Детальная структура тестов](#-детальная-структура-тестов) - Полное описание всех тестов
- [📊 Сводная таблица](#-сводная-таблица-всех-тестов) - Общая статистика
- [🚀 Запуск тестов](#-запуск-тестов) - Как запускать тесты
- [💡 Примеры](#-примеры-правильного-и-неправильного-тестирования) - Правильное и неправильное тестирование

### **Модульная архитектура:**
- **Каждый модуль** - отдельный исполняемый Swift файл с реальной логикой
- **Независимые тесты** - можно запускать по отдельности для отладки
- **Единый test.sh** - запускает все модули автоматически при сборке
- **Расширяемость** - легко добавить новые модули по шаблону
- **Реальные классы** - используют RealInformalSessionDetector, TestablePomodoroTimer, etc.

## 📊 Текущее состояние (обновлено)

**✅ Реализовано и работает:**
- 🧪 **34 автоматических теста** в 7 модулях
- ⚡ **Интеграция в сборку** - тесты запускаются при `./build.sh`
- 🛡️ **Защита от регрессий** - сборка останавливается при провалах
- 🎯 **Реальная логика** - тесты используют настоящие классы приложения
- 📈 **100% успешность** на текущей версии
- ⭐ **Критическое исправление** - тест логики показа окна защищает от регрессии
- 🆕 **Новый тест** - проверка маппинга индексов debug окна (исправление проблемы 7+ дней)

**📋 Статистика:**
- Время выполнения: <1 секунды для всех тестов
- Покрытие: 7 основных компонентов (неформальные сессии, Pomodoro, отдых, показ окна, debug окно)
- Найденные баги: 4 (порог срабатывания, автосброс активности, логика показа окна, маппинг индексов)
- Критические исправления: 2 (проблема с невидимыми окнами, неправильные значения для 7+ дней)

## 🚀 Запуск тестов

### Автоматический запуск при сборке
```bash
./build.sh
```
Тесты запускаются автоматически перед сборкой. Если тесты провалены, сборка останавливается.

### Ручной запуск всех тестов
```bash
./test.sh
```

### Запуск отдельного модуля
```bash
# Неформальные сессии
swift Tests/InformalSessionTest.swift

# Формальные интервалы
swift Tests/PomodoroTimerTest.swift

# Система отдыха
swift Tests/BreakSystemTest.swift

# Маппинг индексов debug окна
swift Tests/DebugWindowMappingTest.swift
```

### Добавление нового модуля тестов

**🎯 Принципы создания новых тестов:**

1. **Используйте РЕАЛЬНЫЕ классы:**
   ```swift
   // ✅ ПРАВИЛЬНО
   let detector = InformalSessionDetector()

   // ❌ НЕПРАВИЛЬНО
   let detector = TestableInformalSessionDetector()
   ```

2. **Изолируйте только внешние зависимости:**
   ```swift
   // ✅ ПРАВИЛЬНО - мокаем UI/файлы/сеть
   let mockUI = MockUserInterface()
   let realDetector = InformalSessionDetector(ui: mockUI)

   // ❌ НЕПРАВИЛЬНО - мокаем основную логику
   let mockDetector = MockInformalSessionDetector()
   ```

3. **Тестируйте реальные сценарии:**
   ```swift
   // ✅ ПРАВИЛЬНО - реальный кейс пользователя
   for _ in 1...52 { detector.recordMinuteActivity(isActive: true) }

   // ❌ НЕПРАВИЛЬНО - искусственный тест
   detector.setActiveMinutes(52)
   ```

**📋 Пошаговая инструкция:**

1. **Скопируйте шаблон** из существующего теста
2. **Замените логику** на тестирование вашего компонента
3. **Используйте реальные классы** вместо упрощенных
4. **Сделайте исполняемым:** `chmod +x Tests/NewTest.swift`
5. **Добавьте в test.sh:** `run_test "Новый модуль" "Tests/NewTest.swift"`
6. **Проверьте:** `swift Tests/NewTest.swift`

## 💡 Примеры правильного и неправильного тестирования

### **✅ ПРАВИЛЬНЫЙ подход:**

```swift
// Тестируем РЕАЛЬНЫЙ InformalSessionDetector
func testRealInformalSession() {
    let detector = InformalSessionDetector() // Реальный класс!

    // Реальный сценарий пользователя
    for _ in 1...52 {
        detector.recordMinuteActivity(isActive: true)
    }

    // Проверяем реальное поведение
    let shouldTrigger = detector.shouldTriggerRestSuggestion()
    assert(shouldTrigger, "52 активных минуты должны вызвать предложение отдыха")
}
```

### **❌ НЕПРАВИЛЬНЫЙ подход:**

```swift
// НЕ создавайте упрощенные версии!
class TestableInformalSessionDetector {  // ❌ Плохо!
    var fakeActiveMinutes = 0

    func setActiveMinutes(_ count: Int) {  // ❌ Искусственно!
        fakeActiveMinutes = count
    }

    func shouldTriggerRestSuggestion() -> Bool {
        return fakeActiveMinutes >= 42  // ❌ Упрощенная логика!
    }
}

func testFakeDetector() {  // ❌ Бесполезный тест!
    let detector = TestableInformalSessionDetector()
    detector.setActiveMinutes(50)  // ❌ Не реальный сценарий!
    assert(detector.shouldTriggerRestSuggestion())  // ❌ Ничего не проверяет!
}
```

### **🎯 Почему реальная логика важна:**

1. **Находит реальные баги** - упрощенные тесты пропускают проблемы
2. **Защищает от регрессий** - изменения в коде сразу ломают тесты
3. **Документирует поведение** - тесты показывают как система работает
4. **Дает уверенность** - если тесты прошли, система реально работает

## 📋 Детальная структура тестов

### **🧪 Модуль 1: InformalSessionTest**
**Файл:** `Tests/InformalSessionTest.swift`
**Описание:** Тестирует логику срабатывания системы неформальных сессий

| № | Название теста | Сценарий | Ожидание | Цель |
|---|---|---|---|---|
| 1 | **Идеальная работа** | 52 активных минуты подряд | ✅ Сработать | Проверка базового срабатывания |
| 2 | **Работа с перерывами** | 45 активных + 7 неактивных | ✅ Сработать | Короткие перерывы не мешают |
| 3 | **Недостаточно активности** | 41 активная + 11 неактивных | ❌ НЕ сработать | Проверка порога 42 минуты |
| 4 | **Граничный случай** | Ровно 42 активных минуты | ✅ Сработать | Минимальный порог срабатывания |
| 5 | **Недостаточно данных** | Только 30 минут данных | ❌ НЕ сработать | Нужно минимум 52 минуты |
| 6 | **Сброс состояния** | Сброс → проверка пустого состояния | ❌ НЕ сработать | Корректность сброса |

**Критерии срабатывания:**
- 📊 **Минимум данных:** 52 минуты
- ⚡ **Минимум активности:** 42 минуты (≥80%)
- 🔄 **Автосброс:** При 15+ неактивных минутах подряд

---

### **⏰ Модуль 2: PomodoroTimerTest**
**Файл:** `Tests/PomodoroTimerTest.swift`
**Описание:** Тестирует логику формальных интервалов (Pomodoro Timer)

| № | Название теста | Сценарий | Ожидание | Цель |
|---|---|---|---|---|
| 1 | **Запуск рабочего интервала** | Вызов startWork() | ✅ Состояние = working | Корректный старт |
| 2 | **Переход на короткий перерыв** | startWork() → startShortBreak() | ✅ Состояние = shortBreak | Переход между состояниями |
| 3 | **Логика длинного перерыва** | 4 интервала → shouldTakeLongBreak() | ✅ true | Каждый 4-й интервал |
| 4 | **Пауза и возобновление** | startWork() → pause() → resume() | ✅ working → paused → working | Управление состоянием |
| 5 | **Остановка таймера** | startWork() → stop() | ✅ idle, счетчик = 0 | Полный сброс |

**Состояния таймера:**
- 🟢 **idle** - ожидание
- 🔴 **working** - рабочий интервал
- ⏸️ **paused** - пауза
- ☕ **shortBreak** - короткий перерыв (5 мин)
- 🛌 **longBreak** - длинный перерыв (15 мин)

---

### **☕ Модуль 3: BreakSystemTest**
**Файл:** `Tests/BreakSystemTest.swift`
**Описание:** Тестирует систему отслеживания качества отдыха

| № | Название теста | Сценарий | Ожидание | Цель |
|---|---|---|---|---|
| 1 | **Начало отслеживания** | startTracking() | ✅ isTracking = true | Активация системы |
| 2 | **Идеальный отдых** | Без записи активности | ✅ Качество = 100% | Полный покой |
| 3 | **Плохой отдых** | 10 активных записей | ✅ Качество = 0% | Постоянная активность |
| 4 | **Средний отдых** | 50% активности | ✅ Качество = 50% | Смешанная активность |
| 5 | **Остановка отслеживания** | stopTracking() | ✅ isTracking = false | Деактивация системы |
| 6 | **Изоляция данных** | Активность без отслеживания | ✅ Качество = 100% | Данные не записываются |

**Формула качества отдыха:**
```
Качество = 100% - (Активные минуты / Общие минуты) × 100%
```

---

## 📊 Сводная таблица всех тестов

| Модуль | Тестов | Компонент | Статус | Время выполнения |
|---|---|---|---|---|
| 🧪 **InformalSessionTest** | 6 | Неформальные сессии | ✅ 100% | <0.001с |
| ⏰ **PomodoroTimerTest** | 5 | Формальные интервалы | ✅ 100% | <0.001с |
| ☕ **BreakSystemTest** | 6 | Система отдыха | ✅ 100% | <0.001с |
| 🪟 **WindowDisplayTest** | 4 | Показ окна (моки) | ✅ 100% | <0.001с |
| 🔗 **IntegratedWindowTest** | 4 | Интегрированные тесты | ✅ 100% | <0.001с |
| ⭐ **WindowLogicTest** | 8 | Логика показа окна | ✅ 100% | <0.001с |
| 🐛 **DebugWindowMappingTest** | 1 | Маппинг debug окна | ✅ 100% | <0.001с |
| 🎨 **TemporarySettingsUITest** | 4 | UI настроек | ✅ 100% | <0.001с |
| 📋 **Планируется...** | - | Детекция активности | 🔄 В разработке | - |
| 📋 **Планируется...** | - | Сон/пробуждение | 🔄 В разработке | - |
| 📋 **Планируется...** | - | Управление проектами | 🔄 В разработке | - |
| 📋 **Планируется...** | - | Статистика | 🔄 В разработке | - |

**📈 Общая статистика:**
- **Всего тестов:** 38 (6+5+6+4+4+8+1+4)
- **Покрытие:** 8 основных компонентов
- **Успешность:** 100% (все тесты проходят)
- **Общее время:** <1 секунды
- **Найденные баги:** 3 (исправлены)

**🎯 Типы тестируемых сценариев:**
- ✅ **Позитивные** - когда система должна сработать
- ❌ **Негативные** - когда система НЕ должна сработать
- ⚖️ **Граничные** - пороговые значения
- 🔄 **Состояния** - переходы между состояниями
- 🧹 **Сброс** - очистка данных и состояний

### **Критерии срабатывания:**
- **Минимум данных:** 52 минуты
- **Минимум активности:** 42 минуты (80%)
- **Формальный таймер:** должен быть неактивен

## 🚀 Запуск тестов

### **Автоматический запуск через интерфейс (рекомендуется)**
1. Запустите приложение uProd
2. Откройте меню "🧪 Другие тесты"
3. Выберите "🤖 Автотест неформальных сессий"
4. Нажмите "Запустить"
5. Получите полный отчет с результатами

### **Ручной запуск через командную строку (для разработчиков)**
```bash
# Запуск всех тестов (требует настройки схемы)
xcodebuild test -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -destination 'platform=macOS'
```

**Примечание:** Интеграция с командной строкой планируется в будущих версиях.

## 📊 Интерпретация результатов

### **Успешный запуск:**
```
Test Suite 'InformalSessionTests' passed at 2025-07-24 12:53:02.123.
	 Executed 8 tests, with 0 failures (0 unexpected) in 0.045 (0.047) seconds
✅ Все тесты прошли успешно!
```

### **Провал тестов:**
```
Test Case '-[SimplePomodoroTestTests.InformalSessionTests testIdealWork52Minutes]' failed (0.001 seconds).
❌ Тесты провалены! Сборка остановлена.
```

## 🔧 Добавление новых тестов

### **Создание нового модуля тестов:**

1. **Создайте файл** `NewModuleTests.swift` в папке `SimplePomodoroTestTests/`

2. **Используйте шаблон:**
```swift
import XCTest
@testable import uProd

class NewModuleTests: XCTestCase {
    
    override func setUp() {
        super.setUp()
        // Инициализация
    }
    
    override func tearDown() {
        // Очистка
        super.tearDown()
    }
    
    func testSpecificScenario() {
        // Arrange: Подготовка
        
        // Act: Действие
        
        // Assert: Проверка
        XCTAssertTrue(condition, "Описание ошибки")
    }
}
```

3. **Добавьте в build.sh:**
```bash
-only-testing:SimplePomodoroTestTests/NewModuleTests
```

### **Добавление теста в существующий модуль:**

1. Добавьте метод `func testNewScenario()` в класс
2. Следуйте паттерну Arrange-Act-Assert
3. Используйте описательные имена и сообщения

## 📋 Планируемые модули тестов

### **PomodoroTimerTests**
- Тестирование формальных интервалов
- Переходы между состояниями
- Обработка пауз и остановок

### **UnifiedReminderTests**
- Система окон и напоминаний
- Логика показа уведомлений
- Обработка пользовательских действий

### **ActivityDetectionTests**
- Определение активности пользователя
- Обработка событий мыши/клавиатуры
- Калибровка чувствительности

### **SleepWakeTests**
- Обработка сна/пробуждения системы
- Сброс счетчиков активности
- Восстановление состояния

### **BreakQualityTests**
- Система оценки качества отдыха
- Анализ активности во время перерыва
- Расчет рекомендаций

### **ProjectManagementTests**
- Создание, редактирование, удаление проектов
- Переключение между проектами
- Сохранение и загрузка данных

### **StatisticsTests**
- Подсчет статистики работы
- Агрегация данных по периодам
- Экспорт отчетов

### **IntegrationTests**
- Взаимодействие между компонентами
- End-to-end сценарии
- Тестирование полного workflow

## 🎯 Лучшие практики

### **Именование тестов:**
- `test[Scenario][ExpectedResult]`
- Пример: `testIdealWork52Minutes`, `testInsufficientActivity`

### **Структура теста:**
```swift
func testScenario() {
    // Arrange: Подготовка данных и состояния
    
    // Act: Выполнение тестируемого действия
    
    // Assert: Проверка результата
    XCTAssertTrue(condition, "Описательное сообщение об ошибке")
}
```

### **Mock объекты:**
- Используйте для изоляции тестируемого компонента
- Наследуйтесь от реальных классов
- Переопределяйте только необходимые методы

### **Сообщения об ошибках:**
- Должны быть описательными
- Включать ожидаемое и фактическое значение
- Помогать в диагностике проблемы

## 🔍 Отладка тестов

### **Просмотр детальных логов:**
```bash
xcodebuild test -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -destination 'platform=macOS' -only-testing:SimplePomodoroTestTests/InformalSessionTests | grep -E "(Test|PASS|FAIL|Error)"
```

### **Запуск в отладочном режиме:**
- Откройте Xcode
- Выберите Test Navigator (⌘6)
- Нажмите на тест правой кнопкой → Debug

### **Добавление точек останова:**
- Поставьте breakpoint в тестовом методе
- Запустите тест в отладочном режиме
- Исследуйте состояние объектов

## 🎯 Модуль 2: PomodoroTimerTest

### **Описание**
Тестирует логику формальных интервалов (Pomodoro Timer) - состояния, переходы, паузы.

### **Покрываемые сценарии (5 тестов):**
1. **Запуск рабочего интервала** - проверка начала работы
2. **Переход на короткий перерыв** - логика перерывов
3. **Логика длинного перерыва** - каждый 4-й интервал
4. **Пауза и возобновление** - управление состоянием
5. **Остановка таймера** - сброс состояния

## ☕ Модуль 3: BreakSystemTest

### **Описание**
Тестирует систему отслеживания качества отдыха и перерывов.

### **Покрываемые сценарии (6 тестов):**
1. **Начало отслеживания** - активация системы
2. **Идеальный отдых** - 100% качества без активности
3. **Плохой отдых** - 0% качества при постоянной активности
4. **Средний отдых** - 50% качества при смешанной активности
5. **Остановка отслеживания** - деактивация системы
6. **Изоляция данных** - активность не записывается без отслеживания

## 📈 Метрики качества

### **Покрытие функциональности:**
- ✅ **Неформальные сессии:** 6 тестов (100% основных сценариев)
- ✅ **Формальные интервалы:** 5 тестов (100% состояний Pomodoro)
- ✅ **Система отдыха:** 6 тестов (100% отслеживания качества)
- 📋 **Планируется:** Детекция активности, сон/пробуждение, статистика

### **Качество тестов:**
- ✅ **Реальная логика:** Используют настоящие классы приложения
- ✅ **Найденные баги:** 2 реальные проблемы обнаружены и исправлены
- ✅ **Детерминированность:** 100% стабильные результаты
- ✅ **Скорость:** <1 секунды для всех 17 тестов

### **Интеграция в разработку:**
- ✅ **Автоматический запуск:** При каждой сборке через `./build.sh`
- ✅ **Остановка при ошибках:** Сборка прерывается если тесты провалены
- ✅ **Четкие отчеты:** Процент успешности и детали провалов
- ✅ **Защита от регрессий:** Реальная проверка качества кода

## 🎯 Планы развития

### **Приоритет 1: Расширение покрытия**
- [ ] **ActivityDetectionTest** - тестирование детекции активности мыши/клавиатуры
- [ ] **SleepWakeTest** - тестирование обработки сна/пробуждения системы
- [ ] **ProjectManagementTest** - тестирование системы проектов и задач
- [ ] **StatisticsTest** - тестирование сбора и анализа статистики

### **Приоритет 2: Углубление качества**
- [ ] **Интеграционные тесты** - взаимодействие между компонентами
- [ ] **Тесты производительности** - проверка скорости критических операций
- [ ] **Стресс-тесты** - поведение при больших объемах данных

### **Приоритет 3: Автоматизация**
- [ ] **CI/CD интеграция** - автоматический запуск при коммитах
- [ ] **Автоматические UI тесты** - проверка интерфейса
- [ ] **Регрессионное тестирование** - автоматическая проверка старых багов

### **⚠️ Принципы для всех новых тестов:**
- **Только реальная логика** - никаких упрощений
- **Максимальная близость к реальности** - тестируем как пользователь
- **Быстрота выполнения** - каждый тест <1 секунды
- **Четкие сообщения об ошибках** - понятно что сломалось и почему

## 🆕 Новые модули тестов (добавлены для исправления критической проблемы)

### **⭐ Модуль 4: Показ окна** (`Tests/WindowDisplayTest.swift`)
**Цель:** Тестирование показа окна неформальных сессий с использованием моков

**Компоненты:**
- `MockInformalSessionDetector` - мок детектора для изоляции логики
- `MockModernCompletionWindow` - мок окна для проверки вызовов

**Ключевые тесты:**
1. **🎯 Воспроизведение проблемы пользователя (43/52 активных минут)**
   - Заполняет точными данными: 8 неактивных + 43 активных + 1 неактивная
   - Проверяет что логика правильно определяет необходимость показа
   - Тестирует callback цепочку от детектора до окна

2. **🔧 Проверка настройки callback**
   - Проверяет что callback `onRestSuggestionNeeded` правильно настроен
   - Тестирует что callback вызывается при срабатывании условий

3. **🪟 Проверка создания и показа окна**
   - Тестирует что окно создается с правильным режимом (`.restSuggestion`)
   - Проверяет что окно показывается и позиционируется

4. **🎯 Граничные случаи**
   - Тестирует поведение на границе (41 vs 42 минуты)
   - Проверяет различные сценарии активности

### **⭐ Модуль 5: Интегрированные тесты окна** (`Tests/IntegratedWindowTest.swift`)
**Цель:** Тестирование с реальными компонентами приложения

**Особенности:**
- Работает с реальными объектами `AppDelegate` и `InformalSessionDetector`
- Использует рефлексию для безопасного взаимодействия с компонентами
- Может запускаться как в контексте приложения, так и автономно

**Функции:**
1. **`runIntegratedWindowTests()`** - полные интегрированные тесты
2. **`quickWindowTest()`** - быстрая проверка основных компонентов
3. **`runAutomatedWindowTests()`** - автоматические тесты для сборки

### **⭐ Модуль 6: Логика показа окна** (`Tests/WindowLogicTest.swift`) **[КРИТИЧЕСКИЙ]**
**Цель:** Тестирование исправления критической проблемы с показом окна

**Проблема:** Окно неформальных сессий не показывалось, несмотря на правильную работу детектора

**Исправление:** Изменена логика проверки видимости окна в `AppDelegate.showUnifiedReminder()`

**Ключевые тесты:**
1. **🔧 Исправленная логика: невидимое окно пересоздается**
   - Проверяет что невидимое окно правильно пересоздается
   - Тестирует новую логику проверки `windowExists && !windowVisible`

2. **👁️ Видимое окно не пересоздается**
   - Проверяет что видимое окно не пересоздается без необходимости
   - Оптимизация производительности

3. **🐛 Демонстрация проблемы старой логики**
   - Сравнивает старую и новую логику
   - Показывает как старая логика НЕ пересоздавала невидимое окно

4. **🎯 Воспроизведение проблемы пользователя**
   - Точная симуляция ситуации пользователя
   - Демонстрирует что исправление решает проблему

5. **🧠 Логика детектора: 43 активные минуты из 52**
   - Проверяет математику определения необходимости показа
   - Тестирует пороговые значения

6. **🎯 Граничные случаи детектора**
   - Тестирует поведение на границе (41 vs 42 минуты)
   - Проверяет точность логики

**Интеграция в сборку:**
- Автоматически запускается при `./build.sh`
- Провал любого теста останавливает сборку
- Защищает от регрессии критического исправления

---

### **🎨 Модуль 8: UI вкладки "Временные"** (`Tests/TemporarySettingsUITest.swift`) **[НОВЫЙ]**
**Цель:** Проверка исправления проблем с интерфейсом настроек

**Проблема:** Выпадающий список позиционирования окон во вкладке "Временные" имел некорректное отображение:
- Огромные отступы справа
- Список не кликался
- Выглядел "стремно" по сравнению с другими вкладками

**Исправление:** Добавлена стилизация и фиксированная ширина для PopUpButton

**Ключевые тесты:**
1. **🎨 Стилизация PopUpButton** - проверяет наличие wantsLayer, cornerRadius, backgroundColor
2. **📏 Фиксированная ширина** - проверяет constraint с шириной 140px
3. **📝 Комментарии об исправлении стилизации** - документирование изменений
4. **📝 Комментарии об исправлении ширины** - документирование изменений

**Особенности:**
- Статический анализ кода (читает файл SettingsWindow.swift)
- Проверяет наличие всех необходимых исправлений
- Быстрое выполнение без UI зависимостей

---

## 🎯 **РЕЗЮМЕ: Главные правила тестирования uProd**

### **✅ ЧТО ДЕЛАТЬ:**
1. **ВСЕГДА начинай с реальных тестов** - они защищают от регрессий
2. **Добавляй интеграционные тесты** - они проверяют полный путь
3. **Mock-тесты только для алгоритмов** - и только в дополнение к реальным

### **❌ ЧТО НЕ ДЕЛАТЬ:**
1. **НЕ полагайся только на Mock-тесты** - они создают иллюзию безопасности
2. **НЕ тестируй только изолированную логику** - компоненты должны работать вместе
3. **НЕ создавай тесты ради тестов** - каждый тест должен находить реальные проблемы

### **🛡️ ЗАЩИТА ОТ РЕГРЕССИЙ:**
- **Реальные тесты** читают исходный код и ловят поломки ✅
- **Mock-тесты** тестируют свою логику и НЕ ловят поломки ❌
- **Интеграционные тесты** проверяют что компоненты работают вместе ✅

### **🚨 ПОМНИ:**
**Цель тестов - защитить от регрессий, а не показать зеленые галочки!**

## 🔥 **КРИТИЧЕСКИ ВАЖНО: Тестирование тестов**

### **⚠️ ГЛАВНАЯ ПРОБЛЕМА: LLM создает ложную безопасность**

**Реальная проблема с AI-тестами:**
- ✅ **LLM обещает** "супер-классные тесты с реальными данными"
- ❌ **По факту** тесты часто НЕ видят реальные ошибки
- 🐛 **Проблема:** AI создает тесты которые выглядят хорошо, но не работают
- 💥 **Результат:** Ложная уверенность в качестве тестов

### **🧪 ОБЯЗАТЕЛЬНАЯ ПРОЦЕДУРА: Тестирование тестов**

**После создания любых тестов ВСЕГДА:**

1. **🔥 Сломай функционал** - внеси ошибку в тестируемый код
2. **🧪 Запусти тесты** - проверь что они упадут
3. **✅ Исправь функционал** - верни правильный код
4. **🧪 Запусти тесты** - проверь что они снова проходят

**Если тесты НЕ упали при поломке - они БЕСПОЛЕЗНЫ!**

### **🎯 Примеры тестирования тестов из проекта uProd:**

#### **❌ Плохой пример: Mock-тесты**
```bash
# Сломали реальную логику в SimpleUnifiedSystem
case 1...2: return 0  // Желтая зона не работает!

# Запустили mock-тесты
./test-unified.sh
# Результат: ✅ ВСЕ ТЕСТЫ ПРОЙДЕНЫ! (ЛОЖЬ!)
```

#### **✅ Хороший пример: Реальные тесты**
```bash
# Сломали ту же логику
case 1...2: return 0  // Желтая зона не работает!

# Запустили реальные тесты
swift Tests/TrueRealUnifiedSystemTest.swift
# Результат: ❌ 0 из 3 тестов прошли - ПОЙМАЛИ ПОЛОМКУ!
```

### **📋 Чек-лист тестирования тестов:**

- [ ] **Сломай критическую логику** (например, желтая зона эскалации)
- [ ] **Запусти тесты** - должны упасть
- [ ] **Сломай граничные значения** (например, границы между зонами)
- [ ] **Запусти тесты** - должны упасть
- [ ] **Сломай основной алгоритм** (например, расчет времени)
- [ ] **Запусти тесты** - должны упасть
- [ ] **Исправь все поломки**
- [ ] **Запусти тесты** - должны пройти

### **🚨 КРАСНЫЕ ФЛАГИ:**

- ❌ **Тесты проходят при сломанной логике** - тесты бесполезны
- ❌ **Тесты используют свою логику** вместо реальной
- ❌ **Тесты не падают при очевидных ошибках** - ложная безопасность
- ❌ **LLM говорит "тесты супер"** без проверки на поломках

### **✅ ХОРОШИЕ ПРИЗНАКИ:**

- ✅ **Тесты падают при поломке** реальной логики
- ✅ **Тесты читают и используют** реальный код
- ✅ **Тесты ловят граничные случаи** и критические ошибки
- ✅ **Проверено тестированием тестов** - сломали и поймали

---

**Последнее обновление:** 25 июля 2025
**Версия документации:** 3.1 (добавлены модули 5-6 для унифицированной системы)
**Автор:** uProd Auto-Testing System

## 🆕 Новые модули тестирования (25 июля 2025)

### **🧪 Модуль 5: UnifiedReminderSystemTest**
**Файл:** `Tests/UnifiedReminderSystemTest.swift`
**Описание:** Проверяет унификацию логики между формальными и неформальными окнами

**Тесты:**
1. **Унификация эскалации** - Формальные и неформальные интервалы используют одинаковую эскалацию
2. **Логика тряски** - Одинаковая логика тряски для всех типов окон
3. **Цветовая схема** - Единая цветовая схема статус-бара
4. **Непрерывность эскалации** - Эскалация работает без остановок
5. **Обновление статус-бара** - Правильное обновление при изменении уровня

### **🧪 Модуль 6: RealLogicIntegrationTest**
**Файл:** `Tests/RealLogicIntegrationTest.swift`
**Описание:** Интеграционные тесты реальной логики без моков

**Тесты:**
1. **OvertimeConfig эскалация** - Правильные уровни эскалации
2. **InformalSessionDetector логика** - Определение длинных сессий
3. **Cooldown система** - Работа системы cooldown
4. **Последовательность эскалации** - Правильная последовательность уровней
5. **Граничные значения** - Обработка границ между уровнями
6. **Производительность** - Быстрая работа OvertimeConfig
7. **Интеграция компонентов** - Совместная работа детектора и конфига

**Цель новых модулей:** Проверить что исправления логики тряски, эскалации и таймеров действительно унифицировали поведение формальных и неформальных окон.

### **⭐ Модуль 7: Новая единая система** (`Tests/UnifiedSystemTest.swift`)
**Цель:** Тестирование архитектуры единой системы напоминаний

**Ключевые тесты:**
- Правильность уровней эскалации OvertimeConfig
- Логика показа напоминаний только при изменении уровня
- Начало с уровня 0 (зеленая зона)
- Обновление статус-бара
- Интеграция с реальными компонентами

**Архитектурные улучшения:** Единая система для формальных и неформальных интервалов

### **⭐ Модуль 8: Поведение кнопки тест** (`Tests/TestButtonBehaviorTest.swift`)
**Цель:** Проверка правильного поведения кнопки "Тест" в настройках

**Ключевые тесты:**
- Тест начинается с уровня 0 (зеленая зона)
- Правильная последовательность уровней (0→1→2→3)
- НЕ начинаем сразу с желтой зоны
- Правильная прогрессия во время теста
- Соответствие цветов уровням

**Исправленные проблемы:** Тест больше не начинается сразу с желтой зоны

## 🎯 Заключение

Система тестирования uProd построена на принципе **"реальная логика прежде всего"**. Это обеспечивает максимальную защиту от регрессий и гарантирует, что тесты действительно проверяют работоспособность приложения.

**Помните:** Лучше иметь меньше тестов, но качественных, чем много поверхностных тестов, которые создают ложное ощущение безопасности.

## 🧪 Новые тесты унифицированной системы активности (2025)

### **🎯 Этап 1: Базовая инфраструктура (ЗАВЕРШЕН)**

#### MinuteActivityTracker Tests
**Файл**: `Tests/MinuteActivityTrackerTest.swift`
**Цель**: Тестирование 20-отрезковой системы обнаружения активности с логикой погрешности

**Покрытие**:
- ✅ Базовая функциональность отслеживания
- ✅ 20-отрезковая проверка активности (20 отрезков по 3 секунды)
- ✅ Логика погрешности: 1 отрезок = случайность, ≥2 отрезков = активность
- ✅ Принудительное завершение минуты
- ✅ Граничные случаи и edge cases
- ✅ Интеграция с UnifiedActivityChecker

**Запуск**: `swift Tests/MinuteActivityTrackerTest.swift`

#### ActivityStateManager Tests
**Файл**: `Tests/ActivityStateManagerTest.swift`
**Цель**: Тестирование машины состояний для управления активностью

**Покрытие**:
- ✅ Базовые переходы состояний (working, awayShort, awayMedium, awayLong, awayVeryLong)
- ✅ Обработка неактивности и остановка счетчиков
- ✅ Состояния отсутствия с временными порогами
- ✅ Сообщения при возвращении (resumeSilently, partialRest, chooseRestOrWork, fullRest)
- ✅ Формальный отдых и специальные состояния

**Запуск**: `swift Tests/ActivityStateManagerTest.swift`

#### Unified Activity System Integration Tests
**Файл**: `Tests/UnifiedActivitySystemTest.swift`
**Цель**: Интеграционное тестирование полной системы

**Покрытие**:
- ✅ Полный сценарий работы системы
- ✅ Интеграция MinuteActivityTracker + ActivityStateManager
- ✅ 5 реальных сценариев: нормальная работа, короткое/среднее/долгое отсутствие, формальный отдых
- ✅ Проверка всех колбэков и событий системы
- ✅ Статистика работы системы

**Запуск**: `swift Tests/UnifiedActivitySystemTest.swift`

### SimpleUnifiedSystem Tests (Предыдущая версия)
**Файл**: `Tests/SimpleUnifiedSystemTest.swift`
**Цель**: Тестирование основной логики унифицированной системы напоминаний

**Покрытие**:
- ✅ Базовый запуск и остановка системы
- ✅ Правильность расчета времени для формальных интервалов
- ✅ Правильность расчета времени для неформальных интервалов
- ✅ Эскалация уровней в тестовом режиме
- ✅ Различие между формальными и неформальными интервалами

**Запуск**: `swift Tests/SimpleUnifiedSystemTest.swift`

### Базовые тесты логики
**Файл**: `Tests/UnifiedSystemBasicTest.swift`
**Цель**: Проверка основной логики без сложных зависимостей

**Покрытие**:
- ✅ Расчет времени для разных типов интервалов
- ✅ Логика уровней эскалации (0-4 уровня)
- ✅ Различия между типами интервалов (показ уровня 0)

**Запуск**: `swift Tests/UnifiedSystemBasicTest.swift`

### Интеграционные тесты
**Файл**: `Tests/IntegrationTest.swift`
**Цель**: Проверка унификации всей системы

**Покрытие**:
- ✅ Унифицированные точки входа (SimpleUnifiedSystem для всех типов)
- ✅ Статистика с типами интервалов ("formal"/"informal")
- ✅ Унифицированные кнопки (фиолетовый цвет, единый текст)

**Запуск**: `swift Tests/IntegrationTest.swift`

### Запуск всех новых тестов
```bash
# Базовые тесты логики
swift Tests/UnifiedSystemBasicTest.swift

# Интеграционные тесты
swift Tests/IntegrationTest.swift

# Полные тесты (требуют настройки окружения)
swift Tests/SimpleUnifiedSystemTest.swift
```

### Реальные тесты унифицированной системы
**Файл**: `Tests/TrueRealUnifiedSystemTest.swift`
**Тип**: РЕАЛЬНЫЕ ТЕСТЫ (читают исходный код)
**Цель**: Защита от регрессий в критической логике

**Особенности**:
- ✅ Загружают реальный файл `SimpleUnifiedSystem.swift`
- ✅ Извлекают функцию `getEscalationLevel` из исходного кода
- ✅ Тестируют РЕАЛЬНУЮ логику, а не упрощенную версию
- ✅ **ПРОВЕРЕНЫ**: ловят реальные ошибки при поломке функционала

**Запуск**: `swift Tests/TrueRealUnifiedSystemTest.swift`

### Тесты критической зоны
**Файл**: `Tests/CriticalZoneTest.swift`
**Тип**: РЕАЛЬНЫЕ ТЕСТЫ (читают исходный код)
**Цель**: Проверка корректности критической зоны (уровень 4+)

**Покрытие**:
- ✅ Критическая зона начинается с 10 минут
- ✅ Границы критической зоны (9 мин = уровень 3, 10+ мин = уровень 4)
- ✅ Экстремальные случаи (60+ минут)
- ✅ **ВАЛИДИРОВАНЫ**: тесты ловят ошибки в логике критической зоны

**Запуск**: `swift Tests/CriticalZoneTest.swift`

### Результаты тестирования
- ✅ **Базовые тесты**: Все пройдены
- ✅ **Интеграционные тесты**: Все пройдены
- ✅ **Реальные тесты**: Все пройдены
- ✅ **Критическая зона**: Все тесты пройдены
- ✅ **Унификация подтверждена**: Формальные и неформальные интервалы используют одинаковую логику

---

## 📈 ОБНОВЛЕНИЕ: Этап 2 интеграции (2025-01-26)

### **✅ ComputerTimeTracker Integration (ЗАВЕРШЕНО)**

**Файл**: `Tests/ComputerTimeTrackerUpgradeTest.swift`
**Цель**: Тестирование интеграции ComputerTimeTracker с новой 20-отрезковой системой

**Что добавлено:**
- ✅ Интеграция с MinuteActivityTracker для более точного обнаружения активности
- ✅ Переключение между старой (60-секундная проверка) и новой (20-отрезковая) системой
- ✅ Обратная совместимость всех существующих интерфейсов
- ✅ Callback система для уведомления о записанной активности
- ✅ Статистика новой системы активности

**Ключевые улучшения:**
- **Точность**: Вместо простой проверки "была ли активность за последние 60 секунд" теперь используется 20-отрезковая система с логикой погрешности (20 отрезков по 3 секунды)
- **Гибкость**: Возможность переключения между старой и новой системой через `setUseNewActivitySystem()`
- **Совместимость**: Все существующие методы работают без изменений

**Результат тестирования**: 5/5 тестов пройдено ✅

**Статус**: Интеграция завершена, проект собирается успешно, обратная совместимость сохранена.

## 🧹 ОПТИМИЗАЦИЯ СИСТЕМЫ ТЕСТИРОВАНИЯ (2025-07-26)

### **🎯 ПРОВЕДЕНА БОЛЬШАЯ ОЧИСТКА**

**Проблемы до оптимизации:**
- ❌ **57 файлов тестов** (10,470 строк кода)
- ❌ **47% мертвого кода** (27 неиспользуемых файлов)
- ❌ **Дублирование номеров модулей** в test.sh
- ❌ **Хаотичная система именования** (Quick*, Real*, Simple*)
- ❌ **Моки вместо реального кода** (ложная безопасность)

**Результаты оптимизации:**
- ✅ **32 рабочих теста** (5,623 строки кода + новые тесты)
- ✅ **0% мертвого кода** (все неиспользуемые архивированы)
- ✅ **Исправлены дубли модулей** (Модуль 11, 12, 13 вместо дублей 7, 8)
- ✅ **Единый стандарт** (@main struct подход)
- ✅ **Все тесты проверены** - 32/32 пройдено ✅
- ✅ **Добавлены тесты интеграции с системой сна** (Модуль 12, 13)

**Архивированные файлы:** `Tests/Archive/` (23 файла, 4,847 строк)

### **📏 НОВЫЕ СТАНДАРТЫ ТЕСТИРОВАНИЯ**

#### **🎯 ЗОЛОТЫЕ ПРАВИЛА (обновлено)**

1. **ОДИН КОМПОНЕНТ = ОДИН ТЕСТ**
   - `MinuteActivityTracker` → `MinuteActivityTrackerTest.swift`
   - `ActivityStateManager` → `ActivityStateManagerTest.swift`
   - НЕ создавай 4 теста для одного компонента!

2. **ЕДИНЫЙ ФОРМАТ: @main struct**
   ```swift
   @main
   struct ComponentNameTest {
       static func main() {
           print("🚀 ТЕСТ ComponentName")

           let component = RealComponent()
           var passed = 0
           var total = 0

           // Сценарий 1: ...
           // Сценарий 2: ...

           print("Результат: \(passed)/\(total)")
       }
   }
   ```

3. **МАКСИМУМ 100 СТРОК НА ТЕСТ**
   - Если больше → разделить на несколько файлов
   - Фокус на критической функциональности

4. **СТАНДАРТ ИМЕНОВАНИЯ**
   ```
   Tests/ComponentNameTest.swift - основной тест
   Tests/ComponentNameIntegrationTest.swift - интеграционный
   ```

5. **ТЕСТИРУЙ РЕАЛЬНЫЙ КОД, НЕ МОКИ**
   - ✅ `let tracker = MinuteActivityTracker()`
   - ❌ `let tracker = MockMinuteActivityTracker()`

#### **🚫 ЧТО БОЛЬШЕ НЕ ДЕЛАЕМ**

- ❌ **НЕ создаем Quick*, Simple*, Real* префиксы** - один тест на компонент
- ❌ **НЕ дублируем тесты** - если есть рабочий тест, не создавай второй
- ❌ **НЕ используем моки для основной логики** - только для внешних зависимостей
- ❌ **НЕ создаем тесты >100 строк** - разделяй на части

#### **✅ ПРОЦЕДУРА ДОБАВЛЕНИЯ НОВОГО ТЕСТА**

1. **Проверь что теста еще нет** - `ls Tests/*ComponentName*`
2. **Используй шаблон @main struct** из `RealMinuteTrackerOnlyTest.swift`
3. **Назови правильно** - `ComponentNameTest.swift`
4. **Добавь в test.sh** - `run_test "Модуль X: Описание" "Tests/ComponentNameTest.swift"`
5. **Проверь что работает** - `swift Tests/ComponentNameTest.swift`
6. **ОБЯЗАТЕЛЬНО: Протестируй тест** - сломай функционал и проверь что тест упадет

## 🚀 Быстрый старт

```bash
# Запустить все тесты (30 тестов)
./test.sh

# Запустить конкретный модуль
swift Tests/InformalSessionTest.swift

# Создать новый тест (используй правильный шаблон!)
cp Tests/RealMinuteTrackerOnlyTest.swift Tests/NewComponentTest.swift
```

## ⚠️ ПОМНИ
- Тесты - это **страховка от регрессий**
- Если тест проходит, но реальный код сломан - тест бесполезен
- Лучше **меньше тестов, но качественных**
- **ОДИН КОМПОНЕНТ = ОДИН ТЕСТ** - не создавай дубли!
- **Система тестирования уже нашла 2 реальных бага!**

## 📋 Новые тесты (2025-07-29)

### **GradualGrowthSystemTest.swift** - Тест градационной системы роста планок
- **Цель:** Проверка новой системы роста планок (замена старого +10%)
- **Тестирует:** Все диапазоны роста (3-7мин: +60%, 8-15мин: +40%, и т.д.)
- **Проверяет:** Скорость восстановления, максимальные ограничения, интеграцию
- **Статус:** ⚠️ Сложный тест с assert-ами (может падать)

### **SimpleGradualGrowthTest.swift** - Простой тест градационной системы
- **Цель:** Базовая проверка работоспособности градационной системы
- **Тестирует:** Основные случаи роста и восстановление с 3 минут
- **Проверяет:** Корректность формул и скорость восстановления
- **Статус:** ✅ Рабочий тест (13 дней восстановления вместо 47 дней при +10%)
- **После оптимизации: 32 рабочих теста, 0% мертвого кода ✅**

## 🆕 Новые тесты интеграции с системой сна

### Модуль 12: SleepIntegrationTest.swift
- **Цель:** Проверка интеграции ActivityStateManager с AppDelegate
- **Что тестирует:**
  - Наличие файлов и классов
  - Интеграция методов resetAfterSleep() и handleReturnAfterInactivity()
  - Удаление TODO комментариев
  - Различение сна и неактивности в коде

### Модуль 13: SleepScenariosTest.swift
- **Цель:** Проверка сценариев сна vs неактивности
- **Что тестирует:**
  - Пороги времени (20 мин для сна, 15 мин для неактивности)
  - Логику различения коротких и длительных событий
  - Вызовы правильных методов для разных сценариев
  - Колбэки и состояния ActivityStateManager

## 🎯 КРИТИЧЕСКОЕ ОБНОВЛЕНИЕ: СИСТЕМА ПОГРЕШНОСТИ (2025-07-27)

### **🚨 ПРОБЛЕМА РЕШЕНА: 53-минутный таймер**

**Проблема**: Пользователь запускал таймер на 3 секунды, уходил от компьютера, но таймер продолжал работать 53+ минут вместо остановки при неактивности.

**Корень проблемы**: Старая система считала минуту "активной" если ЛЮБОЙ из 4 бандов (15-секундных интервалов) содержал активность. При запуске таймера создавалась активность в первом банде, и вся минута считалась активной, даже если пользователь сразу уходил.

**Решение**: Полная переработка MinuteActivityTracker с внедрением **логики погрешности**:

### **🔧 Новая архитектура:**
- **20 отрезков по 3 секунды** вместо 4 бандов по 15 секунд
- **Логика погрешности**: 1 активный отрезок = случайность, ≥2 отрезков = реальная активность
- **Решает проблему**: Запуск таймера (1 отрезок) → минута неактивна → таймер останавливается

### **✅ Результаты тестирования:**
```
🧪 Тест 1: Запуск таймера и уход → ✅ НЕАКТИВНАЯ минута
🧪 Тест 2: Случайное движение мыши → ✅ НЕАКТИВНАЯ минута
🧪 Тест 3: Реальная работа ≥6 секунд → ✅ АКТИВНАЯ минута
🧪 Тест 4: Граничный случай (2 отрезка) → ✅ АКТИВНАЯ минута
```

### **📋 Обновленные файлы:**
- `SimplePomodoroTest/MinuteActivityTracker.swift` - полная переработка
- `SimplePomodoroTest/ActivityStateManager.swift` - обновлены методы
- `UNIFIED_ACTIVITY_SYSTEM.md` - обновлена документация
- `TESTING.md` - добавлена информация о новой системе

### **🎯 Статус**: ✅ ГОТОВО К ИСПОЛЬЗОВАНИЮ
Проблема с 53-минутным таймером полностью решена. Система теперь корректно различает случайную активность от реальной работы.

---

## 🚨 Исправление критического окна (CriticalZoneWindow)

### **🐛 Проблема**: Двойная кнопка в критическом окне
В полноэкранном критическом окне (уровень эскалации 4+) кнопка "Take a break now" отображалась с двойным эффектом - внутри зеленой кнопки была видна еще одна прозрачная кнопка.

### **🔍 Причина**: Неправильная замена слоя кнопки
```swift
// НЕПРАВИЛЬНО - заменяли основной слой NSButton
takeBreakButton.layer = gradientLayer
```

### **✅ Решение**: Правильная работа со слоями
1. **Убрали стандартную рамку**: `takeBreakButton.isBordered = false`
2. **Добавили градиент как подслой**: `layer?.insertSublayer(gradientLayer, at: 0)`
3. **Прозрачный фон основного слоя**: `layer?.backgroundColor = NSColor.clear.cgColor`

### **🧪 Тест**: CriticalZoneWindowTest.swift
- **Файл**: `Tests/CriticalZoneWindowTest.swift`
- **Тестирует**: Правильную стилизацию кнопки без двойного эффекта
- **Запуск**: Меню "🧪 Другие тесты" → "🚨 ТЕСТ КРИТИЧЕСКОГО ОКНА"
- **Статус**: ✅ Исправлено и протестировано

---

## 🆕 **НОВЫЙ КОМПОНЕНТ: CountdownWindow - Обратный отсчет перед критическим окном**

### **📋 Описание проблемы UX**
Критическое окно появлялось внезапно и блокировало весь экран, что могло прервать важную работу пользователя (сохранение файлов, завершение задач).

### **✨ Решение: Компактный предупреждающий обратный отсчет**
- **Компактное окно 300x200** по центру экрана (не полноэкранное!)
- **Прозрачный фон** - пользователь может кликать везде под цифрами
- **Красивая анимация**: scale + fade + цветовая индикация (зеленый → желтый → красный)
- **ESC для отсрочки**: возможность отложить критическое окно на 2 минуты
- **Полупрозрачный фон цифр** для видимости на любом фоне
- **НЕ блокирует работу** - можно сохранять файлы, продолжать работать

### **📁 Новые файлы**
- **`SimplePomodoroTest/CountdownWindow.swift`** - основной компонент
- **`Tests/CountdownWindowTest.swift`** - тесты компонента

### **🧪 Тест**: CountdownWindowTest.swift
- **Файл**: `Tests/CountdownWindowTest.swift`
- **Тестирует**:
  - Существование и структуру файла CountdownWindow.swift
  - Интеграцию с AppDelegate
  - Ключевые элементы (ESC обработка, callback'и)
- **Запуск**: `swift Tests/CountdownWindowTest.swift`
- **Меню тест**: "🧪 Другие тесты" → "⏰ ТЕСТ ОБРАТНОГО ОТСЧЕТА"
- **Статус**: ✅ Реализовано и протестировано

### **🎯 Интеграция**
- **AppDelegate.swift**: Добавлено свойство `countdownWindow` и методы управления
- **Новые методы**: `showCriticalZoneWindow()`, `postponeCriticalZone()`, `testCountdownWindow()`
- **Тестовые пункты меню**: Добавлены для тестирования обеих функций

### **📊 Обновленная статистика тестов**
- **Активные тесты**: 34 файла, 6,100+ строк
- **Новый компонент**: CountdownWindow с полным тестовым покрытием
- **Новый тест**: ButtonMatrixTest.swift (система матрицы кнопок)
- **Новый тест**: ButtonMatrixSyncTest.swift (синхронизация расчетов)
- **Новый тест**: ButtonMatrixRealExamplesTest.swift (проверка реальных примеров)

---

## 🆕 НОВЫЙ ТЕСТ: ButtonMatrixTest.swift (2025-07-30)

### **🎯 Система матрицы кнопок для раннего вовлечения**

**Файл**: `Tests/ButtonMatrixTest.swift`
**Компонент**: `ButtonMatrix` (система генерации кнопок)
**Тип**: РЕАЛЬНЫЕ ТЕСТЫ (тестирует реальную логику)

### **📋 Что тестирует:**
1. **Базовая генерация кнопок** - создание кнопок для всех позиций матрицы
2. **Горизонтальная дескалация** - уменьшение времени (100% → 50% → 15 мин → 3 мин)
3. **Фиксированные уровни** - проверка 15 мин (3-е сообщение) и 3 мин (4-е сообщение)
4. **Вертикальная адаптация** - план-минимум для 7+ дней без работы
5. **Полная сессия (52 мин)** - показ/скрытие в зависимости от размера планки
6. **Полная планка** - показ для горизонтали 1+ (второе сообщение дня)
7. **Полная матрица 5×4** - все 20 комбинаций (дни без работы × время дня)
8. **Форматирование** - корректность отображения в отладочном окне

### **✅ Результаты тестирования:**
```
📊 ИТОГОВЫЙ ОТЧЕТ
✅ Пройдено: 8/8
❌ Провалено: 0/8
📈 Процент успеха: 100%
🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Система ButtonMatrix работает корректно.
```

### **🎪 Примеры работы системы:**
- **0 дней, 1-е сообщение**: `[Начать работу (34 мин)] | [Полная сессия (52 мин)] | [Позже]`
- **0 дней, 2-е сообщение**: `[Начать работу (17 мин)] | [Полная планка (34 мин)] | [Полная сессия (52 мин)] | [Позже]`
- **7+ дней, любое сообщение**: `[Начать работу (3 мин)] | [Полная сессия (52 мин)] | [Позже]`

### **🔧 Интеграция с отладочным окном:**
Заменил захардкоженные кнопки в `EarlyEngagementDebugWindow.swift`:
```swift
// ДО: 🔘 Кнопки: "Начать работу" | "Не сейчас" | "Через 30 мин"
// ПОСЛЕ: 🔘 Кнопки: [Начать работу (35 мин)] | [Полная сессия (52 мин)] | [Позже]
```

### **📚 Документация:**
- `docs/button-matrix-system.md` - полная документация системы
- Архитектура ButtonComponent и ButtonType
- Логика расчета планок (вертикальная + горизонтальная адаптация)
- Примеры использования и интеграции

### **🎯 Значение:**
Система ButtonMatrix решает проблему захардкоженных кнопок и создает основу для предсказуемой, модульной системы генерации кнопок в раннем вовлечении. Теперь каждая позиция матрицы (дни без работы × время дня) генерирует определенный набор кнопок с рассчитанным временем.

**Статус**: ✅ ГОТОВО К ИСПОЛЬЗОВАНИЮ

---

## 🔄 ИСПРАВЛЕНИЕ: ButtonMatrixSyncTest.swift (2025-07-30)

### **🐛 Проблема: Несоответствие расчетов**

**Обнаружена критическая проблема**: ButtonMatrix и EarlyEngagementSystem использовали разную логику расчета, что приводило к несоответствию между отображаемой "рассчитанной планкой" и реальными кнопками.

### **📋 Примеры проблем:**
- **Пример 1**: "рассчитанная планка: 35 мин" → кнопка "34 мин"
- **Пример 2**: "рассчитанная планка: 8 мин" → кнопка "14 мин"

### **🔧 Исправления:**

1. **Горизонтальная дескалация**:
   - **Было**: 100% → 50% → **ФИКСИРОВАННО 15 мин** → 3 мин
   - **Стало**: 100% → 50% → **25%** → 3 мин

2. **Вертикальная адаптация (уровень 0)**:
   - **Было**: простое `× 1.14`
   - **Стало**: `GradualGrowthSystem.applyLevel0Growth()`

### **🧪 Тест синхронизации:**

**Файл**: `Tests/ButtonMatrixSyncTest.swift`
**Цель**: Проверить что ButtonMatrix и EarlyEngagementSystem дают одинаковые результаты

### **📊 Результаты теста:**
- ✅ **100% тестов пройдено** (7/7)
- ✅ **Все расчеты синхронизированы**
- ✅ **Примеры из проблемы исправлены**

**Команда запуска**: `swift Tests/ButtonMatrixSyncTest.swift`

### **🎯 Итог:**
Теперь отладочное окно показывает **точно те же значения** что и генерируемые кнопки. Проблема несоответствия полностью решена.

---

## 🔧 ФИНАЛЬНОЕ ИСПРАВЛЕНИЕ: ButtonMatrixRealExamplesTest.swift (2025-07-30)

### **🐛 Обнаружена дополнительная проблема:**

После первого исправления пользователь сообщил о продолжающихся несоответствиях:
- **Пример**: "рассчитанная планка: 5 мин" → кнопка "9 мин"
- **Пример**: "рассчитанная планка: 3 мин" → кнопка "11 мин"

### **🔍 Корень проблемы:**

**Неправильный маппинг дней без работы** в `ButtonMatrix.generateButtonsForEarlyEngagement()`:

```swift
// БЫЛО (НЕПРАВИЛЬНО):
let vertical = min(4, max(0, daysWithoutWork))
// Проблема: 7+ дней → vertical=4 (логика 4-6 дней)

// СТАЛО (ПРАВИЛЬНО):
let vertical = max(0, daysWithoutWork)
// Решение: 7+ дней → vertical=7 (логика default/7+)
```

### **🧪 Тест реальных примеров:**

**Файл**: `Tests/ButtonMatrixRealExamplesTest.swift`
**Цель**: Проверить конкретные примеры из отладочного окна

### **📊 Результаты:**
- ✅ **Пример 1**: 4-6 дней, 20 мин → 5 мин (синхронизировано)
- ✅ **Пример 2**: 7+ дней, 40 мин → 3 мин план-минимум (синхронизировано)
- ✅ **Пример 3**: 6 дней, 30 мин → 8 мин (граничный случай работает)

**Команда запуска**: `swift Tests/ButtonMatrixRealExamplesTest.swift`

### **🎯 Финальный результат:**
**100% синхронизация достигнута!** Все реальные примеры из отладочного окна теперь показывают одинаковые значения в "рассчитанной планке" и кнопках.
